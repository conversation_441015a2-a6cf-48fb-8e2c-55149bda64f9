# Deployment Customization Guide

This guide is for development and deployment teams to customize the application branding and appearance.

## Quick Start

1. **Locate the configuration file**: `public/config.json`
2. **Edit the configuration** with your custom values
3. **Deploy or refresh** the application

## Configuration Options

### Application Name

Change the browser tab title:

```json
{
  "ui": {
    "appName": "Your Custom App Name"
  }
}
```

### Custom Favicon

Replace the browser tab icon:

```json
{
  "ui": {
    "favicon": "/your-custom-favicon.ico"
  }
}
```

### Custom Logo

Replace the header logo:

```json
{
  "ui": {
    "logoImageSource": "/your-logo.png"
  }
}
```

## Complete Example

```json
{
  "ui": {
    "favicon": "/acme-favicon.ico",
    "appName": "ACME Corporation Dashboard"
  },
  "company": {
    "companyLogo": "/acme-logo.svg",
    "companyMiniLogo": "/acme-mini-logo.png",
    "companyName": "ACME Corporation"
  },
  "navigation": {
    "dashboard": {
      "show": true
    },
    "users": {
      "show": true,
      "userList": true,
      "userRoles": true
    },
    "reports": {
      "show": true,
      "salesReport": true,
      "userReport": false
    },
    "settings": {
      "show": false,
      "general": false,
      "notifications": false
    }
  }
}
```

## Navigation Configuration

Control which menu items are visible in the navigation. **All items are hidden by default** and must be explicitly enabled.

### Main Navigation Items

- `navigation.dashboard.show` - Shows/hides Dashboard menu item
- `navigation.users.show` - Shows/hides Users menu item
- `navigation.reports.show` - Shows/hides Reports menu item
- `navigation.settings.show` - Shows/hides Settings menu item

### Sub-Navigation Items

- `navigation.users.userList` - Shows/hides "User List" sub-item
- `navigation.users.userRoles` - Shows/hides "User Roles" sub-item
- `navigation.reports.salesReport` - Shows/hides "Sales Report" sub-item
- `navigation.reports.userReport` - Shows/hides "User Report" sub-item
- `navigation.settings.general` - Shows/hides "General" sub-item
- `navigation.settings.notifications` - Shows/hides "Notifications" sub-item

### Navigation Rules

1. **Optional Configuration**: If navigation section is not included, all items are hidden
2. **Parent Required**: Sub-items only show if parent item `show` is `true`
3. **Default Hidden**: All items default to `false` if not specified
4. **Explicit Enable**: Set to `true` to make items visible

## Company Branding Configuration

Control how your company branding appears in the application header with flexible fallback options.

### Branding Options

#### Full Company Logo

- **Property**: `company.companyLogo`
- **Usage**: Complete branding solution - shows only the logo
- **Best for**: Companies with recognizable logo marks
- **Example**: `"/company-logo.svg"` or `"https://cdn.company.com/logo.png"`

#### Mini Logo + Company Name

- **Properties**: `company.companyMiniLogo` + `company.companyName`
- **Usage**: Small icon/logo alongside company name text
- **Best for**: Companies wanting both visual and text branding
- **Example**: Mini logo: `"/icon.png"`, Name: `"TechStart Solutions"`

#### Company Name Only

- **Property**: `company.companyName`
- **Usage**: Text-based branding with generated avatar
- **Best for**: Text-focused branding or companies without logos
- **Example**: `"Innovate Inc"` (shows "I" in avatar + "Innovate Inc" text)

#### Default Branding

- **Properties**: All empty (`""`)
- **Usage**: Generic branding
- **Shows**: Avatar with "C" + "Company" text

### Branding Priority Rules

1. **`companyLogo`** → Full logo (overrides everything)
2. **`companyMiniLogo` + `companyName`** → Icon + text
3. **`companyName` only** → Generated avatar + text
4. **All empty** → Default "C" avatar + "Company"

## Asset Management

### Local Assets

Place image files in the `public/` directory:

```
public/
├── config.json
├── acme-favicon.ico
├── acme-logo.svg
└── acme-logo.png
```

### External Assets

Use full URLs for externally hosted assets:

```json
{
  "ui": {
    "logoImageSource": "https://cdn.acme.com/logo.svg"
  }
}
```

## Logo Guidelines

### Recommended Specifications

- **Format**: SVG, PNG, or JPG
- **Height**: 32px (will auto-scale width)
- **Max Width**: 200px (prevents header overflow)
- **Background**: Transparent or matching header color

### Logo Placement

The logo replaces the default "C" avatar and "Company" text in the header. If no logo is specified, the default branding is shown.

## Deployment Workflows

### Development

1. Edit `public/config.json`
2. Refresh browser to see changes
3. Commit changes to version control

### Production Deployment

1. Update `config.json` on production server
2. Changes are immediate (no restart required)
3. Users see changes on next page load

### Docker Deployment

```dockerfile
# Option 1: Copy during build
COPY config.json /app/public/config.json

# Option 2: Mount as volume (recommended)
# docker run -v /host/config.json:/app/public/config.json
```

### CI/CD Integration

```yaml
# Example GitHub Actions step
- name: Deploy Custom Configuration
  run: |
    echo '${{ secrets.APP_CONFIG }}' > public/config.json
    # Deploy application
```

## Validation

### Test Your Configuration

1. Ensure JSON is valid (use a JSON validator)
2. Verify image URLs are accessible
3. Test in different browsers
4. Check mobile responsiveness

### Common Issues

- **Invalid JSON**: Application falls back to defaults
- **Missing images**: Broken image icons appear
- **Large logos**: May cause header layout issues
- **CORS errors**: External images may not load

## Environment-Specific Configurations

### Development (Full Access)

```json
{
  "ui": {
    "appName": "MyApp - Development",
    "logoImageSource": "/dev-logo.png"
  },
  "navigation": {
    "dashboard": { "show": true },
    "users": { "show": true, "userList": true, "userRoles": true },
    "reports": { "show": true, "salesReport": true, "userReport": true },
    "settings": { "show": true, "general": true, "notifications": true }
  }
}
```

### Staging (Limited Access)

```json
{
  "ui": {
    "appName": "MyApp - Staging",
    "logoImageSource": "/staging-logo.png"
  },
  "navigation": {
    "dashboard": { "show": true },
    "users": { "show": true, "userList": true, "userRoles": false },
    "reports": { "show": true, "salesReport": true, "userReport": false },
    "settings": { "show": false }
  }
}
```

### Production (Minimal Access)

```json
{
  "ui": {
    "appName": "MyApp",
    "logoImageSource": "/production-logo.png"
  },
  "navigation": {
    "dashboard": { "show": true },
    "users": { "show": true, "userList": true, "userRoles": false },
    "reports": { "show": false },
    "settings": { "show": false }
  }
}
```

## Security Notes

- Configuration file is publicly accessible
- Never include sensitive data (API keys, passwords)
- Only development/deployment team should modify
- End users cannot change configuration through the app

## Support

For technical issues with configuration:

1. Check browser console for errors
2. Verify JSON syntax
3. Test image URLs directly
4. Contact development team

---

**Note**: This configuration system is designed for deployment-time customization only. End users do not have access to modify these settings through the application interface.
