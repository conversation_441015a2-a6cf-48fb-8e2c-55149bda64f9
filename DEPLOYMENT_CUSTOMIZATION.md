# Deployment Customization Guide

This guide is for development and deployment teams to customize the application branding and appearance.

## Quick Start

1. **Locate the configuration file**: `public/config.json`
2. **Edit the configuration** with your custom values
3. **Deploy or refresh** the application

## Configuration Options

### Application Name
Change the browser tab title:
```json
{
  "ui": {
    "appName": "Your Custom App Name"
  }
}
```

### Custom Favicon
Replace the browser tab icon:
```json
{
  "ui": {
    "favicon": "/your-custom-favicon.ico"
  }
}
```

### Custom Logo
Replace the header logo:
```json
{
  "ui": {
    "logoImageSource": "/your-logo.png"
  }
}
```

## Complete Example

```json
{
  "ui": {
    "favicon": "/acme-favicon.ico",
    "appName": "ACME Corporation Dashboard",
    "logoImageSource": "/acme-logo.svg"
  }
}
```

## Asset Management

### Local Assets
Place image files in the `public/` directory:
```
public/
├── config.json
├── acme-favicon.ico
├── acme-logo.svg
└── acme-logo.png
```

### External Assets
Use full URLs for externally hosted assets:
```json
{
  "ui": {
    "logoImageSource": "https://cdn.acme.com/logo.svg"
  }
}
```

## Logo Guidelines

### Recommended Specifications
- **Format**: SVG, PNG, or JPG
- **Height**: 32px (will auto-scale width)
- **Max Width**: 200px (prevents header overflow)
- **Background**: Transparent or matching header color

### Logo Placement
The logo replaces the default "C" avatar and "Company" text in the header. If no logo is specified, the default branding is shown.

## Deployment Workflows

### Development
1. Edit `public/config.json`
2. Refresh browser to see changes
3. Commit changes to version control

### Production Deployment
1. Update `config.json` on production server
2. Changes are immediate (no restart required)
3. Users see changes on next page load

### Docker Deployment
```dockerfile
# Option 1: Copy during build
COPY config.json /app/public/config.json

# Option 2: Mount as volume (recommended)
# docker run -v /host/config.json:/app/public/config.json
```

### CI/CD Integration
```yaml
# Example GitHub Actions step
- name: Deploy Custom Configuration
  run: |
    echo '${{ secrets.APP_CONFIG }}' > public/config.json
    # Deploy application
```

## Validation

### Test Your Configuration
1. Ensure JSON is valid (use a JSON validator)
2. Verify image URLs are accessible
3. Test in different browsers
4. Check mobile responsiveness

### Common Issues
- **Invalid JSON**: Application falls back to defaults
- **Missing images**: Broken image icons appear
- **Large logos**: May cause header layout issues
- **CORS errors**: External images may not load

## Environment-Specific Configurations

### Development
```json
{
  "ui": {
    "appName": "MyApp - Development",
    "logoImageSource": "/dev-logo.png"
  }
}
```

### Staging
```json
{
  "ui": {
    "appName": "MyApp - Staging",
    "logoImageSource": "/staging-logo.png"
  }
}
```

### Production
```json
{
  "ui": {
    "appName": "MyApp",
    "logoImageSource": "/production-logo.png"
  }
}
```

## Security Notes

- Configuration file is publicly accessible
- Never include sensitive data (API keys, passwords)
- Only development/deployment team should modify
- End users cannot change configuration through the app

## Support

For technical issues with configuration:
1. Check browser console for errors
2. Verify JSON syntax
3. Test image URLs directly
4. Contact development team

---

**Note**: This configuration system is designed for deployment-time customization only. End users do not have access to modify these settings through the application interface.
