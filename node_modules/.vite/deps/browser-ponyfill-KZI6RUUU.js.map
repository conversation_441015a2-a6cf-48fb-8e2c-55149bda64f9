{"version": 3, "sources": ["../../cross-fetch/dist/browser-ponyfill.js"], "sourcesContent": ["// Save global object in a variable\nvar __global__ =\n(typeof globalThis !== 'undefined' && globalThis) ||\n(typeof self !== 'undefined' && self) ||\n(typeof global !== 'undefined' && global);\n// Create an object that extends from __global__ without the fetch function\nvar __globalThis__ = (function () {\nfunction F() {\nthis.fetch = false;\nthis.DOMException = __global__.DOMException\n}\nF.prototype = __global__; // Needed for feature detection on whatwg-fetch's code\nreturn new F();\n})();\n// Wraps whatwg-fetch with a function scope to hijack the global object\n// \"globalThis\" that's going to be patched\n(function(globalThis) {\n\nvar irrelevant = (function (exports) {\n\n  var global =\n    (typeof globalThis !== 'undefined' && globalThis) ||\n    (typeof self !== 'undefined' && self) ||\n    (typeof global !== 'undefined' && global);\n\n  var support = {\n    searchParams: 'URLSearchParams' in global,\n    iterable: 'Symbol' in global && 'iterator' in Symbol,\n    blob:\n      'FileReader' in global &&\n      'Blob' in global &&\n      (function() {\n        try {\n          new Blob();\n          return true\n        } catch (e) {\n          return false\n        }\n      })(),\n    formData: 'FormData' in global,\n    arrayBuffer: 'ArrayBuffer' in global\n  };\n\n  function isDataView(obj) {\n    return obj && DataView.prototype.isPrototypeOf(obj)\n  }\n\n  if (support.arrayBuffer) {\n    var viewClasses = [\n      '[object Int8Array]',\n      '[object Uint8Array]',\n      '[object Uint8ClampedArray]',\n      '[object Int16Array]',\n      '[object Uint16Array]',\n      '[object Int32Array]',\n      '[object Uint32Array]',\n      '[object Float32Array]',\n      '[object Float64Array]'\n    ];\n\n    var isArrayBufferView =\n      ArrayBuffer.isView ||\n      function(obj) {\n        return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n      };\n  }\n\n  function normalizeName(name) {\n    if (typeof name !== 'string') {\n      name = String(name);\n    }\n    if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n      throw new TypeError('Invalid character in header field name: \"' + name + '\"')\n    }\n    return name.toLowerCase()\n  }\n\n  function normalizeValue(value) {\n    if (typeof value !== 'string') {\n      value = String(value);\n    }\n    return value\n  }\n\n  // Build a destructive iterator for the value list\n  function iteratorFor(items) {\n    var iterator = {\n      next: function() {\n        var value = items.shift();\n        return {done: value === undefined, value: value}\n      }\n    };\n\n    if (support.iterable) {\n      iterator[Symbol.iterator] = function() {\n        return iterator\n      };\n    }\n\n    return iterator\n  }\n\n  function Headers(headers) {\n    this.map = {};\n\n    if (headers instanceof Headers) {\n      headers.forEach(function(value, name) {\n        this.append(name, value);\n      }, this);\n    } else if (Array.isArray(headers)) {\n      headers.forEach(function(header) {\n        this.append(header[0], header[1]);\n      }, this);\n    } else if (headers) {\n      Object.getOwnPropertyNames(headers).forEach(function(name) {\n        this.append(name, headers[name]);\n      }, this);\n    }\n  }\n\n  Headers.prototype.append = function(name, value) {\n    name = normalizeName(name);\n    value = normalizeValue(value);\n    var oldValue = this.map[name];\n    this.map[name] = oldValue ? oldValue + ', ' + value : value;\n  };\n\n  Headers.prototype['delete'] = function(name) {\n    delete this.map[normalizeName(name)];\n  };\n\n  Headers.prototype.get = function(name) {\n    name = normalizeName(name);\n    return this.has(name) ? this.map[name] : null\n  };\n\n  Headers.prototype.has = function(name) {\n    return this.map.hasOwnProperty(normalizeName(name))\n  };\n\n  Headers.prototype.set = function(name, value) {\n    this.map[normalizeName(name)] = normalizeValue(value);\n  };\n\n  Headers.prototype.forEach = function(callback, thisArg) {\n    for (var name in this.map) {\n      if (this.map.hasOwnProperty(name)) {\n        callback.call(thisArg, this.map[name], name, this);\n      }\n    }\n  };\n\n  Headers.prototype.keys = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push(name);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.values = function() {\n    var items = [];\n    this.forEach(function(value) {\n      items.push(value);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.entries = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push([name, value]);\n    });\n    return iteratorFor(items)\n  };\n\n  if (support.iterable) {\n    Headers.prototype[Symbol.iterator] = Headers.prototype.entries;\n  }\n\n  function consumed(body) {\n    if (body.bodyUsed) {\n      return Promise.reject(new TypeError('Already read'))\n    }\n    body.bodyUsed = true;\n  }\n\n  function fileReaderReady(reader) {\n    return new Promise(function(resolve, reject) {\n      reader.onload = function() {\n        resolve(reader.result);\n      };\n      reader.onerror = function() {\n        reject(reader.error);\n      };\n    })\n  }\n\n  function readBlobAsArrayBuffer(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsArrayBuffer(blob);\n    return promise\n  }\n\n  function readBlobAsText(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsText(blob);\n    return promise\n  }\n\n  function readArrayBufferAsText(buf) {\n    var view = new Uint8Array(buf);\n    var chars = new Array(view.length);\n\n    for (var i = 0; i < view.length; i++) {\n      chars[i] = String.fromCharCode(view[i]);\n    }\n    return chars.join('')\n  }\n\n  function bufferClone(buf) {\n    if (buf.slice) {\n      return buf.slice(0)\n    } else {\n      var view = new Uint8Array(buf.byteLength);\n      view.set(new Uint8Array(buf));\n      return view.buffer\n    }\n  }\n\n  function Body() {\n    this.bodyUsed = false;\n\n    this._initBody = function(body) {\n      /*\n        fetch-mock wraps the Response object in an ES6 Proxy to\n        provide useful test harness features such as flush. However, on\n        ES5 browsers without fetch or Proxy support pollyfills must be used;\n        the proxy-pollyfill is unable to proxy an attribute unless it exists\n        on the object before the Proxy is created. This change ensures\n        Response.bodyUsed exists on the instance, while maintaining the\n        semantic of setting Request.bodyUsed in the constructor before\n        _initBody is called.\n      */\n      this.bodyUsed = this.bodyUsed;\n      this._bodyInit = body;\n      if (!body) {\n        this._bodyText = '';\n      } else if (typeof body === 'string') {\n        this._bodyText = body;\n      } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n        this._bodyBlob = body;\n      } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n        this._bodyFormData = body;\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this._bodyText = body.toString();\n      } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n        this._bodyArrayBuffer = bufferClone(body.buffer);\n        // IE 10-11 can't handle a DataView body.\n        this._bodyInit = new Blob([this._bodyArrayBuffer]);\n      } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n        this._bodyArrayBuffer = bufferClone(body);\n      } else {\n        this._bodyText = body = Object.prototype.toString.call(body);\n      }\n\n      if (!this.headers.get('content-type')) {\n        if (typeof body === 'string') {\n          this.headers.set('content-type', 'text/plain;charset=UTF-8');\n        } else if (this._bodyBlob && this._bodyBlob.type) {\n          this.headers.set('content-type', this._bodyBlob.type);\n        } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n          this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n        }\n      }\n    };\n\n    if (support.blob) {\n      this.blob = function() {\n        var rejected = consumed(this);\n        if (rejected) {\n          return rejected\n        }\n\n        if (this._bodyBlob) {\n          return Promise.resolve(this._bodyBlob)\n        } else if (this._bodyArrayBuffer) {\n          return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n        } else if (this._bodyFormData) {\n          throw new Error('could not read FormData body as blob')\n        } else {\n          return Promise.resolve(new Blob([this._bodyText]))\n        }\n      };\n\n      this.arrayBuffer = function() {\n        if (this._bodyArrayBuffer) {\n          var isConsumed = consumed(this);\n          if (isConsumed) {\n            return isConsumed\n          }\n          if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n            return Promise.resolve(\n              this._bodyArrayBuffer.buffer.slice(\n                this._bodyArrayBuffer.byteOffset,\n                this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength\n              )\n            )\n          } else {\n            return Promise.resolve(this._bodyArrayBuffer)\n          }\n        } else {\n          return this.blob().then(readBlobAsArrayBuffer)\n        }\n      };\n    }\n\n    this.text = function() {\n      var rejected = consumed(this);\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return readBlobAsText(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as text')\n      } else {\n        return Promise.resolve(this._bodyText)\n      }\n    };\n\n    if (support.formData) {\n      this.formData = function() {\n        return this.text().then(decode)\n      };\n    }\n\n    this.json = function() {\n      return this.text().then(JSON.parse)\n    };\n\n    return this\n  }\n\n  // HTTP methods whose capitalization should be normalized\n  var methods = ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'POST', 'PUT'];\n\n  function normalizeMethod(method) {\n    var upcased = method.toUpperCase();\n    return methods.indexOf(upcased) > -1 ? upcased : method\n  }\n\n  function Request(input, options) {\n    if (!(this instanceof Request)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n\n    options = options || {};\n    var body = options.body;\n\n    if (input instanceof Request) {\n      if (input.bodyUsed) {\n        throw new TypeError('Already read')\n      }\n      this.url = input.url;\n      this.credentials = input.credentials;\n      if (!options.headers) {\n        this.headers = new Headers(input.headers);\n      }\n      this.method = input.method;\n      this.mode = input.mode;\n      this.signal = input.signal;\n      if (!body && input._bodyInit != null) {\n        body = input._bodyInit;\n        input.bodyUsed = true;\n      }\n    } else {\n      this.url = String(input);\n    }\n\n    this.credentials = options.credentials || this.credentials || 'same-origin';\n    if (options.headers || !this.headers) {\n      this.headers = new Headers(options.headers);\n    }\n    this.method = normalizeMethod(options.method || this.method || 'GET');\n    this.mode = options.mode || this.mode || null;\n    this.signal = options.signal || this.signal;\n    this.referrer = null;\n\n    if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n      throw new TypeError('Body not allowed for GET or HEAD requests')\n    }\n    this._initBody(body);\n\n    if (this.method === 'GET' || this.method === 'HEAD') {\n      if (options.cache === 'no-store' || options.cache === 'no-cache') {\n        // Search for a '_' parameter in the query string\n        var reParamSearch = /([?&])_=[^&]*/;\n        if (reParamSearch.test(this.url)) {\n          // If it already exists then set the value with the current time\n          this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime());\n        } else {\n          // Otherwise add a new '_' parameter to the end with the current time\n          var reQueryString = /\\?/;\n          this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime();\n        }\n      }\n    }\n  }\n\n  Request.prototype.clone = function() {\n    return new Request(this, {body: this._bodyInit})\n  };\n\n  function decode(body) {\n    var form = new FormData();\n    body\n      .trim()\n      .split('&')\n      .forEach(function(bytes) {\n        if (bytes) {\n          var split = bytes.split('=');\n          var name = split.shift().replace(/\\+/g, ' ');\n          var value = split.join('=').replace(/\\+/g, ' ');\n          form.append(decodeURIComponent(name), decodeURIComponent(value));\n        }\n      });\n    return form\n  }\n\n  function parseHeaders(rawHeaders) {\n    var headers = new Headers();\n    // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n    // https://tools.ietf.org/html/rfc7230#section-3.2\n    var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ');\n    // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n    // https://github.com/github/fetch/issues/748\n    // https://github.com/zloirock/core-js/issues/751\n    preProcessedHeaders\n      .split('\\r')\n      .map(function(header) {\n        return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header\n      })\n      .forEach(function(line) {\n        var parts = line.split(':');\n        var key = parts.shift().trim();\n        if (key) {\n          var value = parts.join(':').trim();\n          headers.append(key, value);\n        }\n      });\n    return headers\n  }\n\n  Body.call(Request.prototype);\n\n  function Response(bodyInit, options) {\n    if (!(this instanceof Response)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n    if (!options) {\n      options = {};\n    }\n\n    this.type = 'default';\n    this.status = options.status === undefined ? 200 : options.status;\n    this.ok = this.status >= 200 && this.status < 300;\n    this.statusText = options.statusText === undefined ? '' : '' + options.statusText;\n    this.headers = new Headers(options.headers);\n    this.url = options.url || '';\n    this._initBody(bodyInit);\n  }\n\n  Body.call(Response.prototype);\n\n  Response.prototype.clone = function() {\n    return new Response(this._bodyInit, {\n      status: this.status,\n      statusText: this.statusText,\n      headers: new Headers(this.headers),\n      url: this.url\n    })\n  };\n\n  Response.error = function() {\n    var response = new Response(null, {status: 0, statusText: ''});\n    response.type = 'error';\n    return response\n  };\n\n  var redirectStatuses = [301, 302, 303, 307, 308];\n\n  Response.redirect = function(url, status) {\n    if (redirectStatuses.indexOf(status) === -1) {\n      throw new RangeError('Invalid status code')\n    }\n\n    return new Response(null, {status: status, headers: {location: url}})\n  };\n\n  exports.DOMException = global.DOMException;\n  try {\n    new exports.DOMException();\n  } catch (err) {\n    exports.DOMException = function(message, name) {\n      this.message = message;\n      this.name = name;\n      var error = Error(message);\n      this.stack = error.stack;\n    };\n    exports.DOMException.prototype = Object.create(Error.prototype);\n    exports.DOMException.prototype.constructor = exports.DOMException;\n  }\n\n  function fetch(input, init) {\n    return new Promise(function(resolve, reject) {\n      var request = new Request(input, init);\n\n      if (request.signal && request.signal.aborted) {\n        return reject(new exports.DOMException('Aborted', 'AbortError'))\n      }\n\n      var xhr = new XMLHttpRequest();\n\n      function abortXhr() {\n        xhr.abort();\n      }\n\n      xhr.onload = function() {\n        var options = {\n          status: xhr.status,\n          statusText: xhr.statusText,\n          headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n        };\n        options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');\n        var body = 'response' in xhr ? xhr.response : xhr.responseText;\n        setTimeout(function() {\n          resolve(new Response(body, options));\n        }, 0);\n      };\n\n      xhr.onerror = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request failed'));\n        }, 0);\n      };\n\n      xhr.ontimeout = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request failed'));\n        }, 0);\n      };\n\n      xhr.onabort = function() {\n        setTimeout(function() {\n          reject(new exports.DOMException('Aborted', 'AbortError'));\n        }, 0);\n      };\n\n      function fixUrl(url) {\n        try {\n          return url === '' && global.location.href ? global.location.href : url\n        } catch (e) {\n          return url\n        }\n      }\n\n      xhr.open(request.method, fixUrl(request.url), true);\n\n      if (request.credentials === 'include') {\n        xhr.withCredentials = true;\n      } else if (request.credentials === 'omit') {\n        xhr.withCredentials = false;\n      }\n\n      if ('responseType' in xhr) {\n        if (support.blob) {\n          xhr.responseType = 'blob';\n        } else if (\n          support.arrayBuffer &&\n          request.headers.get('Content-Type') &&\n          request.headers.get('Content-Type').indexOf('application/octet-stream') !== -1\n        ) {\n          xhr.responseType = 'arraybuffer';\n        }\n      }\n\n      if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers)) {\n        Object.getOwnPropertyNames(init.headers).forEach(function(name) {\n          xhr.setRequestHeader(name, normalizeValue(init.headers[name]));\n        });\n      } else {\n        request.headers.forEach(function(value, name) {\n          xhr.setRequestHeader(name, value);\n        });\n      }\n\n      if (request.signal) {\n        request.signal.addEventListener('abort', abortXhr);\n\n        xhr.onreadystatechange = function() {\n          // DONE (success or failure)\n          if (xhr.readyState === 4) {\n            request.signal.removeEventListener('abort', abortXhr);\n          }\n        };\n      }\n\n      xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit);\n    })\n  }\n\n  fetch.polyfill = true;\n\n  if (!global.fetch) {\n    global.fetch = fetch;\n    global.Headers = Headers;\n    global.Request = Request;\n    global.Response = Response;\n  }\n\n  exports.Headers = Headers;\n  exports.Request = Request;\n  exports.Response = Response;\n  exports.fetch = fetch;\n\n  return exports;\n\n})({});\n})(__globalThis__);\n// This is a ponyfill, so...\n__globalThis__.fetch.ponyfill = true;\ndelete __globalThis__.fetch.polyfill;\n// Choose between native implementation (__global__) or custom implementation (__globalThis__)\nvar ctx = __global__.fetch ? __global__ : __globalThis__;\nexports = ctx.fetch // To enable: import fetch from 'cross-fetch'\nexports.default = ctx.fetch // For TypeScript consumers without esModuleInterop.\nexports.fetch = ctx.fetch // To enable: import {fetch} from 'cross-fetch'\nexports.Headers = ctx.Headers\nexports.Request = ctx.Request\nexports.Response = ctx.Response\nmodule.exports = exports\n"], "mappings": ";;;;;AAAA;AAAA;AACA,QAAI,aACH,OAAO,eAAe,eAAe,cACrC,OAAO,SAAS,eAAe,QAC/B,OAAO,WAAW,eAAe;AAElC,QAAI,iBAAkB,WAAY;AAClC,eAAS,IAAI;AACb,aAAK,QAAQ;AACb,aAAK,eAAe,WAAW;AAAA,MAC/B;AACA,QAAE,YAAY;AACd,aAAO,IAAI,EAAE;AAAA,IACb,EAAG;AAGH,KAAC,SAASA,aAAY;AAEtB,UAAI,aAAc,SAAUC,UAAS;AAEnC,YAAIC,UACD,OAAOF,gBAAe,eAAeA,eACrC,OAAO,SAAS,eAAe,QAC/B,OAAOE,YAAW,eAAeA;AAEpC,YAAI,UAAU;AAAA,UACZ,cAAc,qBAAqBA;AAAA,UACnC,UAAU,YAAYA,WAAU,cAAc;AAAA,UAC9C,MACE,gBAAgBA,WAChB,UAAUA,WACT,WAAW;AACV,gBAAI;AACF,kBAAI,KAAK;AACT,qBAAO;AAAA,YACT,SAAS,GAAG;AACV,qBAAO;AAAA,YACT;AAAA,UACF,EAAG;AAAA,UACL,UAAU,cAAcA;AAAA,UACxB,aAAa,iBAAiBA;AAAA,QAChC;AAEA,iBAAS,WAAW,KAAK;AACvB,iBAAO,OAAO,SAAS,UAAU,cAAc,GAAG;AAAA,QACpD;AAEA,YAAI,QAAQ,aAAa;AACvB,cAAI,cAAc;AAAA,YAChB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAEA,cAAI,oBACF,YAAY,UACZ,SAAS,KAAK;AACZ,mBAAO,OAAO,YAAY,QAAQ,OAAO,UAAU,SAAS,KAAK,GAAG,CAAC,IAAI;AAAA,UAC3E;AAAA,QACJ;AAEA,iBAAS,cAAc,MAAM;AAC3B,cAAI,OAAO,SAAS,UAAU;AAC5B,mBAAO,OAAO,IAAI;AAAA,UACpB;AACA,cAAI,6BAA6B,KAAK,IAAI,KAAK,SAAS,IAAI;AAC1D,kBAAM,IAAI,UAAU,8CAA8C,OAAO,GAAG;AAAA,UAC9E;AACA,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,iBAAS,eAAe,OAAO;AAC7B,cAAI,OAAO,UAAU,UAAU;AAC7B,oBAAQ,OAAO,KAAK;AAAA,UACtB;AACA,iBAAO;AAAA,QACT;AAGA,iBAAS,YAAY,OAAO;AAC1B,cAAI,WAAW;AAAA,YACb,MAAM,WAAW;AACf,kBAAI,QAAQ,MAAM,MAAM;AACxB,qBAAO,EAAC,MAAM,UAAU,QAAW,MAAY;AAAA,YACjD;AAAA,UACF;AAEA,cAAI,QAAQ,UAAU;AACpB,qBAAS,OAAO,QAAQ,IAAI,WAAW;AACrC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,QAAQ,SAAS;AACxB,eAAK,MAAM,CAAC;AAEZ,cAAI,mBAAmB,SAAS;AAC9B,oBAAQ,QAAQ,SAAS,OAAO,MAAM;AACpC,mBAAK,OAAO,MAAM,KAAK;AAAA,YACzB,GAAG,IAAI;AAAA,UACT,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,oBAAQ,QAAQ,SAAS,QAAQ;AAC/B,mBAAK,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,YAClC,GAAG,IAAI;AAAA,UACT,WAAW,SAAS;AAClB,mBAAO,oBAAoB,OAAO,EAAE,QAAQ,SAAS,MAAM;AACzD,mBAAK,OAAO,MAAM,QAAQ,IAAI,CAAC;AAAA,YACjC,GAAG,IAAI;AAAA,UACT;AAAA,QACF;AAEA,gBAAQ,UAAU,SAAS,SAAS,MAAM,OAAO;AAC/C,iBAAO,cAAc,IAAI;AACzB,kBAAQ,eAAe,KAAK;AAC5B,cAAI,WAAW,KAAK,IAAI,IAAI;AAC5B,eAAK,IAAI,IAAI,IAAI,WAAW,WAAW,OAAO,QAAQ;AAAA,QACxD;AAEA,gBAAQ,UAAU,QAAQ,IAAI,SAAS,MAAM;AAC3C,iBAAO,KAAK,IAAI,cAAc,IAAI,CAAC;AAAA,QACrC;AAEA,gBAAQ,UAAU,MAAM,SAAS,MAAM;AACrC,iBAAO,cAAc,IAAI;AACzB,iBAAO,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,QAC3C;AAEA,gBAAQ,UAAU,MAAM,SAAS,MAAM;AACrC,iBAAO,KAAK,IAAI,eAAe,cAAc,IAAI,CAAC;AAAA,QACpD;AAEA,gBAAQ,UAAU,MAAM,SAAS,MAAM,OAAO;AAC5C,eAAK,IAAI,cAAc,IAAI,CAAC,IAAI,eAAe,KAAK;AAAA,QACtD;AAEA,gBAAQ,UAAU,UAAU,SAAS,UAAU,SAAS;AACtD,mBAAS,QAAQ,KAAK,KAAK;AACzB,gBAAI,KAAK,IAAI,eAAe,IAAI,GAAG;AACjC,uBAAS,KAAK,SAAS,KAAK,IAAI,IAAI,GAAG,MAAM,IAAI;AAAA,YACnD;AAAA,UACF;AAAA,QACF;AAEA,gBAAQ,UAAU,OAAO,WAAW;AAClC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAO,MAAM;AACjC,kBAAM,KAAK,IAAI;AAAA,UACjB,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,gBAAQ,UAAU,SAAS,WAAW;AACpC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAO;AAC3B,kBAAM,KAAK,KAAK;AAAA,UAClB,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,gBAAQ,UAAU,UAAU,WAAW;AACrC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAO,MAAM;AACjC,kBAAM,KAAK,CAAC,MAAM,KAAK,CAAC;AAAA,UAC1B,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,YAAI,QAAQ,UAAU;AACpB,kBAAQ,UAAU,OAAO,QAAQ,IAAI,QAAQ,UAAU;AAAA,QACzD;AAEA,iBAAS,SAAS,MAAM;AACtB,cAAI,KAAK,UAAU;AACjB,mBAAO,QAAQ,OAAO,IAAI,UAAU,cAAc,CAAC;AAAA,UACrD;AACA,eAAK,WAAW;AAAA,QAClB;AAEA,iBAAS,gBAAgB,QAAQ;AAC/B,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,mBAAO,SAAS,WAAW;AACzB,sBAAQ,OAAO,MAAM;AAAA,YACvB;AACA,mBAAO,UAAU,WAAW;AAC1B,qBAAO,OAAO,KAAK;AAAA,YACrB;AAAA,UACF,CAAC;AAAA,QACH;AAEA,iBAAS,sBAAsB,MAAM;AACnC,cAAI,SAAS,IAAI,WAAW;AAC5B,cAAI,UAAU,gBAAgB,MAAM;AACpC,iBAAO,kBAAkB,IAAI;AAC7B,iBAAO;AAAA,QACT;AAEA,iBAAS,eAAe,MAAM;AAC5B,cAAI,SAAS,IAAI,WAAW;AAC5B,cAAI,UAAU,gBAAgB,MAAM;AACpC,iBAAO,WAAW,IAAI;AACtB,iBAAO;AAAA,QACT;AAEA,iBAAS,sBAAsB,KAAK;AAClC,cAAI,OAAO,IAAI,WAAW,GAAG;AAC7B,cAAI,QAAQ,IAAI,MAAM,KAAK,MAAM;AAEjC,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,CAAC,IAAI,OAAO,aAAa,KAAK,CAAC,CAAC;AAAA,UACxC;AACA,iBAAO,MAAM,KAAK,EAAE;AAAA,QACtB;AAEA,iBAAS,YAAY,KAAK;AACxB,cAAI,IAAI,OAAO;AACb,mBAAO,IAAI,MAAM,CAAC;AAAA,UACpB,OAAO;AACL,gBAAI,OAAO,IAAI,WAAW,IAAI,UAAU;AACxC,iBAAK,IAAI,IAAI,WAAW,GAAG,CAAC;AAC5B,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAEA,iBAAS,OAAO;AACd,eAAK,WAAW;AAEhB,eAAK,YAAY,SAAS,MAAM;AAW9B,iBAAK,WAAW,KAAK;AACrB,iBAAK,YAAY;AACjB,gBAAI,CAAC,MAAM;AACT,mBAAK,YAAY;AAAA,YACnB,WAAW,OAAO,SAAS,UAAU;AACnC,mBAAK,YAAY;AAAA,YACnB,WAAW,QAAQ,QAAQ,KAAK,UAAU,cAAc,IAAI,GAAG;AAC7D,mBAAK,YAAY;AAAA,YACnB,WAAW,QAAQ,YAAY,SAAS,UAAU,cAAc,IAAI,GAAG;AACrE,mBAAK,gBAAgB;AAAA,YACvB,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,mBAAK,YAAY,KAAK,SAAS;AAAA,YACjC,WAAW,QAAQ,eAAe,QAAQ,QAAQ,WAAW,IAAI,GAAG;AAClE,mBAAK,mBAAmB,YAAY,KAAK,MAAM;AAE/C,mBAAK,YAAY,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC;AAAA,YACnD,WAAW,QAAQ,gBAAgB,YAAY,UAAU,cAAc,IAAI,KAAK,kBAAkB,IAAI,IAAI;AACxG,mBAAK,mBAAmB,YAAY,IAAI;AAAA,YAC1C,OAAO;AACL,mBAAK,YAAY,OAAO,OAAO,UAAU,SAAS,KAAK,IAAI;AAAA,YAC7D;AAEA,gBAAI,CAAC,KAAK,QAAQ,IAAI,cAAc,GAAG;AACrC,kBAAI,OAAO,SAAS,UAAU;AAC5B,qBAAK,QAAQ,IAAI,gBAAgB,0BAA0B;AAAA,cAC7D,WAAW,KAAK,aAAa,KAAK,UAAU,MAAM;AAChD,qBAAK,QAAQ,IAAI,gBAAgB,KAAK,UAAU,IAAI;AAAA,cACtD,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,qBAAK,QAAQ,IAAI,gBAAgB,iDAAiD;AAAA,cACpF;AAAA,YACF;AAAA,UACF;AAEA,cAAI,QAAQ,MAAM;AAChB,iBAAK,OAAO,WAAW;AACrB,kBAAI,WAAW,SAAS,IAAI;AAC5B,kBAAI,UAAU;AACZ,uBAAO;AAAA,cACT;AAEA,kBAAI,KAAK,WAAW;AAClB,uBAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,cACvC,WAAW,KAAK,kBAAkB;AAChC,uBAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC,CAAC;AAAA,cAC1D,WAAW,KAAK,eAAe;AAC7B,sBAAM,IAAI,MAAM,sCAAsC;AAAA,cACxD,OAAO;AACL,uBAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC;AAAA,cACnD;AAAA,YACF;AAEA,iBAAK,cAAc,WAAW;AAC5B,kBAAI,KAAK,kBAAkB;AACzB,oBAAI,aAAa,SAAS,IAAI;AAC9B,oBAAI,YAAY;AACd,yBAAO;AAAA,gBACT;AACA,oBAAI,YAAY,OAAO,KAAK,gBAAgB,GAAG;AAC7C,yBAAO,QAAQ;AAAA,oBACb,KAAK,iBAAiB,OAAO;AAAA,sBAC3B,KAAK,iBAAiB;AAAA,sBACtB,KAAK,iBAAiB,aAAa,KAAK,iBAAiB;AAAA,oBAC3D;AAAA,kBACF;AAAA,gBACF,OAAO;AACL,yBAAO,QAAQ,QAAQ,KAAK,gBAAgB;AAAA,gBAC9C;AAAA,cACF,OAAO;AACL,uBAAO,KAAK,KAAK,EAAE,KAAK,qBAAqB;AAAA,cAC/C;AAAA,YACF;AAAA,UACF;AAEA,eAAK,OAAO,WAAW;AACrB,gBAAI,WAAW,SAAS,IAAI;AAC5B,gBAAI,UAAU;AACZ,qBAAO;AAAA,YACT;AAEA,gBAAI,KAAK,WAAW;AAClB,qBAAO,eAAe,KAAK,SAAS;AAAA,YACtC,WAAW,KAAK,kBAAkB;AAChC,qBAAO,QAAQ,QAAQ,sBAAsB,KAAK,gBAAgB,CAAC;AAAA,YACrE,WAAW,KAAK,eAAe;AAC7B,oBAAM,IAAI,MAAM,sCAAsC;AAAA,YACxD,OAAO;AACL,qBAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,YACvC;AAAA,UACF;AAEA,cAAI,QAAQ,UAAU;AACpB,iBAAK,WAAW,WAAW;AACzB,qBAAO,KAAK,KAAK,EAAE,KAAK,MAAM;AAAA,YAChC;AAAA,UACF;AAEA,eAAK,OAAO,WAAW;AACrB,mBAAO,KAAK,KAAK,EAAE,KAAK,KAAK,KAAK;AAAA,UACpC;AAEA,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,CAAC,UAAU,OAAO,QAAQ,WAAW,QAAQ,KAAK;AAEhE,iBAAS,gBAAgB,QAAQ;AAC/B,cAAI,UAAU,OAAO,YAAY;AACjC,iBAAO,QAAQ,QAAQ,OAAO,IAAI,KAAK,UAAU;AAAA,QACnD;AAEA,iBAAS,QAAQ,OAAO,SAAS;AAC/B,cAAI,EAAE,gBAAgB,UAAU;AAC9B,kBAAM,IAAI,UAAU,4FAA4F;AAAA,UAClH;AAEA,oBAAU,WAAW,CAAC;AACtB,cAAI,OAAO,QAAQ;AAEnB,cAAI,iBAAiB,SAAS;AAC5B,gBAAI,MAAM,UAAU;AAClB,oBAAM,IAAI,UAAU,cAAc;AAAA,YACpC;AACA,iBAAK,MAAM,MAAM;AACjB,iBAAK,cAAc,MAAM;AACzB,gBAAI,CAAC,QAAQ,SAAS;AACpB,mBAAK,UAAU,IAAI,QAAQ,MAAM,OAAO;AAAA,YAC1C;AACA,iBAAK,SAAS,MAAM;AACpB,iBAAK,OAAO,MAAM;AAClB,iBAAK,SAAS,MAAM;AACpB,gBAAI,CAAC,QAAQ,MAAM,aAAa,MAAM;AACpC,qBAAO,MAAM;AACb,oBAAM,WAAW;AAAA,YACnB;AAAA,UACF,OAAO;AACL,iBAAK,MAAM,OAAO,KAAK;AAAA,UACzB;AAEA,eAAK,cAAc,QAAQ,eAAe,KAAK,eAAe;AAC9D,cAAI,QAAQ,WAAW,CAAC,KAAK,SAAS;AACpC,iBAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAAA,UAC5C;AACA,eAAK,SAAS,gBAAgB,QAAQ,UAAU,KAAK,UAAU,KAAK;AACpE,eAAK,OAAO,QAAQ,QAAQ,KAAK,QAAQ;AACzC,eAAK,SAAS,QAAQ,UAAU,KAAK;AACrC,eAAK,WAAW;AAEhB,eAAK,KAAK,WAAW,SAAS,KAAK,WAAW,WAAW,MAAM;AAC7D,kBAAM,IAAI,UAAU,2CAA2C;AAAA,UACjE;AACA,eAAK,UAAU,IAAI;AAEnB,cAAI,KAAK,WAAW,SAAS,KAAK,WAAW,QAAQ;AACnD,gBAAI,QAAQ,UAAU,cAAc,QAAQ,UAAU,YAAY;AAEhE,kBAAI,gBAAgB;AACpB,kBAAI,cAAc,KAAK,KAAK,GAAG,GAAG;AAEhC,qBAAK,MAAM,KAAK,IAAI,QAAQ,eAAe,UAAS,oBAAI,KAAK,GAAE,QAAQ,CAAC;AAAA,cAC1E,OAAO;AAEL,oBAAI,gBAAgB;AACpB,qBAAK,QAAQ,cAAc,KAAK,KAAK,GAAG,IAAI,MAAM,OAAO,QAAO,oBAAI,KAAK,GAAE,QAAQ;AAAA,cACrF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,gBAAQ,UAAU,QAAQ,WAAW;AACnC,iBAAO,IAAI,QAAQ,MAAM,EAAC,MAAM,KAAK,UAAS,CAAC;AAAA,QACjD;AAEA,iBAAS,OAAO,MAAM;AACpB,cAAI,OAAO,IAAI,SAAS;AACxB,eACG,KAAK,EACL,MAAM,GAAG,EACT,QAAQ,SAAS,OAAO;AACvB,gBAAI,OAAO;AACT,kBAAI,QAAQ,MAAM,MAAM,GAAG;AAC3B,kBAAI,OAAO,MAAM,MAAM,EAAE,QAAQ,OAAO,GAAG;AAC3C,kBAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,QAAQ,OAAO,GAAG;AAC9C,mBAAK,OAAO,mBAAmB,IAAI,GAAG,mBAAmB,KAAK,CAAC;AAAA,YACjE;AAAA,UACF,CAAC;AACH,iBAAO;AAAA,QACT;AAEA,iBAAS,aAAa,YAAY;AAChC,cAAI,UAAU,IAAI,QAAQ;AAG1B,cAAI,sBAAsB,WAAW,QAAQ,gBAAgB,GAAG;AAIhE,8BACG,MAAM,IAAI,EACV,IAAI,SAAS,QAAQ;AACpB,mBAAO,OAAO,QAAQ,IAAI,MAAM,IAAI,OAAO,OAAO,GAAG,OAAO,MAAM,IAAI;AAAA,UACxE,CAAC,EACA,QAAQ,SAAS,MAAM;AACtB,gBAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,gBAAI,MAAM,MAAM,MAAM,EAAE,KAAK;AAC7B,gBAAI,KAAK;AACP,kBAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,KAAK;AACjC,sBAAQ,OAAO,KAAK,KAAK;AAAA,YAC3B;AAAA,UACF,CAAC;AACH,iBAAO;AAAA,QACT;AAEA,aAAK,KAAK,QAAQ,SAAS;AAE3B,iBAAS,SAAS,UAAU,SAAS;AACnC,cAAI,EAAE,gBAAgB,WAAW;AAC/B,kBAAM,IAAI,UAAU,4FAA4F;AAAA,UAClH;AACA,cAAI,CAAC,SAAS;AACZ,sBAAU,CAAC;AAAA,UACb;AAEA,eAAK,OAAO;AACZ,eAAK,SAAS,QAAQ,WAAW,SAAY,MAAM,QAAQ;AAC3D,eAAK,KAAK,KAAK,UAAU,OAAO,KAAK,SAAS;AAC9C,eAAK,aAAa,QAAQ,eAAe,SAAY,KAAK,KAAK,QAAQ;AACvE,eAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAC1C,eAAK,MAAM,QAAQ,OAAO;AAC1B,eAAK,UAAU,QAAQ;AAAA,QACzB;AAEA,aAAK,KAAK,SAAS,SAAS;AAE5B,iBAAS,UAAU,QAAQ,WAAW;AACpC,iBAAO,IAAI,SAAS,KAAK,WAAW;AAAA,YAClC,QAAQ,KAAK;AAAA,YACb,YAAY,KAAK;AAAA,YACjB,SAAS,IAAI,QAAQ,KAAK,OAAO;AAAA,YACjC,KAAK,KAAK;AAAA,UACZ,CAAC;AAAA,QACH;AAEA,iBAAS,QAAQ,WAAW;AAC1B,cAAI,WAAW,IAAI,SAAS,MAAM,EAAC,QAAQ,GAAG,YAAY,GAAE,CAAC;AAC7D,mBAAS,OAAO;AAChB,iBAAO;AAAA,QACT;AAEA,YAAI,mBAAmB,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAE/C,iBAAS,WAAW,SAAS,KAAK,QAAQ;AACxC,cAAI,iBAAiB,QAAQ,MAAM,MAAM,IAAI;AAC3C,kBAAM,IAAI,WAAW,qBAAqB;AAAA,UAC5C;AAEA,iBAAO,IAAI,SAAS,MAAM,EAAC,QAAgB,SAAS,EAAC,UAAU,IAAG,EAAC,CAAC;AAAA,QACtE;AAEA,QAAAD,SAAQ,eAAeC,QAAO;AAC9B,YAAI;AACF,cAAID,SAAQ,aAAa;AAAA,QAC3B,SAAS,KAAK;AACZ,UAAAA,SAAQ,eAAe,SAAS,SAAS,MAAM;AAC7C,iBAAK,UAAU;AACf,iBAAK,OAAO;AACZ,gBAAI,QAAQ,MAAM,OAAO;AACzB,iBAAK,QAAQ,MAAM;AAAA,UACrB;AACA,UAAAA,SAAQ,aAAa,YAAY,OAAO,OAAO,MAAM,SAAS;AAC9D,UAAAA,SAAQ,aAAa,UAAU,cAAcA,SAAQ;AAAA,QACvD;AAEA,iBAAS,MAAM,OAAO,MAAM;AAC1B,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,gBAAI,UAAU,IAAI,QAAQ,OAAO,IAAI;AAErC,gBAAI,QAAQ,UAAU,QAAQ,OAAO,SAAS;AAC5C,qBAAO,OAAO,IAAIA,SAAQ,aAAa,WAAW,YAAY,CAAC;AAAA,YACjE;AAEA,gBAAI,MAAM,IAAI,eAAe;AAE7B,qBAAS,WAAW;AAClB,kBAAI,MAAM;AAAA,YACZ;AAEA,gBAAI,SAAS,WAAW;AACtB,kBAAI,UAAU;AAAA,gBACZ,QAAQ,IAAI;AAAA,gBACZ,YAAY,IAAI;AAAA,gBAChB,SAAS,aAAa,IAAI,sBAAsB,KAAK,EAAE;AAAA,cACzD;AACA,sBAAQ,MAAM,iBAAiB,MAAM,IAAI,cAAc,QAAQ,QAAQ,IAAI,eAAe;AAC1F,kBAAI,OAAO,cAAc,MAAM,IAAI,WAAW,IAAI;AAClD,yBAAW,WAAW;AACpB,wBAAQ,IAAI,SAAS,MAAM,OAAO,CAAC;AAAA,cACrC,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,UAAU,WAAW;AACvB,yBAAW,WAAW;AACpB,uBAAO,IAAI,UAAU,wBAAwB,CAAC;AAAA,cAChD,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,YAAY,WAAW;AACzB,yBAAW,WAAW;AACpB,uBAAO,IAAI,UAAU,wBAAwB,CAAC;AAAA,cAChD,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,UAAU,WAAW;AACvB,yBAAW,WAAW;AACpB,uBAAO,IAAIA,SAAQ,aAAa,WAAW,YAAY,CAAC;AAAA,cAC1D,GAAG,CAAC;AAAA,YACN;AAEA,qBAAS,OAAO,KAAK;AACnB,kBAAI;AACF,uBAAO,QAAQ,MAAMC,QAAO,SAAS,OAAOA,QAAO,SAAS,OAAO;AAAA,cACrE,SAAS,GAAG;AACV,uBAAO;AAAA,cACT;AAAA,YACF;AAEA,gBAAI,KAAK,QAAQ,QAAQ,OAAO,QAAQ,GAAG,GAAG,IAAI;AAElD,gBAAI,QAAQ,gBAAgB,WAAW;AACrC,kBAAI,kBAAkB;AAAA,YACxB,WAAW,QAAQ,gBAAgB,QAAQ;AACzC,kBAAI,kBAAkB;AAAA,YACxB;AAEA,gBAAI,kBAAkB,KAAK;AACzB,kBAAI,QAAQ,MAAM;AAChB,oBAAI,eAAe;AAAA,cACrB,WACE,QAAQ,eACR,QAAQ,QAAQ,IAAI,cAAc,KAClC,QAAQ,QAAQ,IAAI,cAAc,EAAE,QAAQ,0BAA0B,MAAM,IAC5E;AACA,oBAAI,eAAe;AAAA,cACrB;AAAA,YACF;AAEA,gBAAI,QAAQ,OAAO,KAAK,YAAY,YAAY,EAAE,KAAK,mBAAmB,UAAU;AAClF,qBAAO,oBAAoB,KAAK,OAAO,EAAE,QAAQ,SAAS,MAAM;AAC9D,oBAAI,iBAAiB,MAAM,eAAe,KAAK,QAAQ,IAAI,CAAC,CAAC;AAAA,cAC/D,CAAC;AAAA,YACH,OAAO;AACL,sBAAQ,QAAQ,QAAQ,SAAS,OAAO,MAAM;AAC5C,oBAAI,iBAAiB,MAAM,KAAK;AAAA,cAClC,CAAC;AAAA,YACH;AAEA,gBAAI,QAAQ,QAAQ;AAClB,sBAAQ,OAAO,iBAAiB,SAAS,QAAQ;AAEjD,kBAAI,qBAAqB,WAAW;AAElC,oBAAI,IAAI,eAAe,GAAG;AACxB,0BAAQ,OAAO,oBAAoB,SAAS,QAAQ;AAAA,gBACtD;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,KAAK,OAAO,QAAQ,cAAc,cAAc,OAAO,QAAQ,SAAS;AAAA,UAC9E,CAAC;AAAA,QACH;AAEA,cAAM,WAAW;AAEjB,YAAI,CAACA,QAAO,OAAO;AACjB,UAAAA,QAAO,QAAQ;AACf,UAAAA,QAAO,UAAU;AACjB,UAAAA,QAAO,UAAU;AACjB,UAAAA,QAAO,WAAW;AAAA,QACpB;AAEA,QAAAD,SAAQ,UAAU;AAClB,QAAAA,SAAQ,UAAU;AAClB,QAAAA,SAAQ,WAAW;AACnB,QAAAA,SAAQ,QAAQ;AAEhB,eAAOA;AAAA,MAET,EAAG,CAAC,CAAC;AAAA,IACL,GAAG,cAAc;AAEjB,mBAAe,MAAM,WAAW;AAChC,WAAO,eAAe,MAAM;AAE5B,QAAI,MAAM,WAAW,QAAQ,aAAa;AAC1C,cAAU,IAAI;AACd,YAAQ,UAAU,IAAI;AACtB,YAAQ,QAAQ,IAAI;AACpB,YAAQ,UAAU,IAAI;AACtB,YAAQ,UAAU,IAAI;AACtB,YAAQ,WAAW,IAAI;AACvB,WAAO,UAAU;AAAA;AAAA;", "names": ["globalThis", "exports", "global"]}