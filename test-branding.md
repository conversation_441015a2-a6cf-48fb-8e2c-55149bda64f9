# Company Branding Test Guide

## Test Scenarios

### 1. Full Company Logo (Priority 1)
**Config**: Copy content from `public/config-examples/config-full-logo.json` to `public/config.json`
**Expected Result**: Shows only the full company logo, ignores mini logo and company name

### 2. Mini Logo + Company Name (Priority 2)  
**Config**: Copy content from `public/config-examples/config-mini-logo.json` to `public/config.json`
**Expected Result**: Shows mini logo icon + "TechStart Solutions" text

### 3. Company Name Only (Priority 3)
**Config**: Copy content from `public/config-examples/config-name-only.json` to `public/config.json`  
**Expected Result**: Shows avatar with "I" + "Innovate Inc" text

### 4. Default Branding (Priority 4)
**Config**: Copy content from `public/config-examples/config-defaults.json` to `public/config.json`
**Expected Result**: Shows avatar with "C" + "Company" text

## Current Configuration
The current config.json shows:
- Mini logo: `/favicon-excelfore.ico` 
- Company name: "SOTA UI - React"
- Expected: Shows favicon as mini logo + "SOTA UI - React" text

## Testing Steps
1. Navigate to http://localhost:5173
2. Look at the header left section
3. Verify the branding matches expected result
4. Test different configurations by replacing config.json content
5. Refresh browser to see changes

## Branding Logic Verification
✅ Priority 1: `companyLogo` set → Show full logo only
✅ Priority 2: `companyLogo` empty + `companyMiniLogo` set → Show mini logo + name  
✅ Priority 3: Both logos empty + `companyName` set → Show avatar with first letter + name
✅ Priority 4: All empty → Show default "C" avatar + "Company"
