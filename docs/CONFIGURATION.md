# Application Configuration System

The React application supports dynamic configuration through a `config.json` file that is loaded at runtime. This allows for customization without rebuilding the application.

## Configuration File Location

The configuration file must be placed at:
```
public/config.json
```

This file is served statically and loaded by the application when it starts.

## Configuration Schema

```json
{
  "ui": {
    "favicon": "string (optional)",
    "appName": "string (optional)", 
    "logoImageSource": "string (optional)"
  }
}
```

## Configuration Properties

### `ui.favicon`
- **Type**: `string` (URL)
- **Default**: `/favicon-excelfore.ico`
- **Description**: URL to the favicon icon displayed in the browser tab
- **Example**: `"/custom-favicon.ico"` or `"https://example.com/favicon.ico"`

### `ui.appName`
- **Type**: `string`
- **Default**: `"eSync SOTA"`
- **Description**: Application name displayed in the browser tab title
- **Example**: `"My Custom App"`

### `ui.logoImageSource`
- **Type**: `string` (URL)
- **Default**: `""` (shows default avatar + "Company" text)
- **Description**: URL to the logo image displayed in the application header
- **Example**: `"/logo.png"` or `"https://example.com/logo.svg"`

## Example Configurations

### Minimal Configuration (Use Defaults)
```json
{
  "ui": {
    "favicon": "",
    "appName": "",
    "logoImageSource": ""
  }
}
```

### Custom Branding
```json
{
  "ui": {
    "favicon": "/custom-favicon.ico",
    "appName": "Acme Corporation Dashboard",
    "logoImageSource": "/acme-logo.png"
  }
}
```

### External Resources
```json
{
  "ui": {
    "favicon": "https://cdn.example.com/favicon.ico",
    "appName": "Enterprise Portal",
    "logoImageSource": "https://cdn.example.com/logo.svg"
  }
}
```

## How It Works

1. **Application Startup**: The `ConfigProvider` component loads `config.json` from the public directory
2. **Merging**: Configuration values are merged with defaults to ensure all properties exist
3. **Document Updates**: The system automatically updates:
   - Document title (`<title>` tag)
   - Favicon (`<link rel="icon">` tag)
4. **Component Updates**: React components use the `useConfig()` hook to access configuration
5. **Error Handling**: If config loading fails, default values are used

## Usage in Components

```tsx
import { useConfig } from '../contexts/ConfigContext';

const MyComponent: React.FC = () => {
  const { config, loading, error } = useConfig();
  
  if (loading) return <div>Loading configuration...</div>;
  if (error) return <div>Error: {error}</div>;
  
  return (
    <div>
      <h1>{config.ui.appName}</h1>
      {config.ui.logoImageSource && (
        <img src={config.ui.logoImageSource} alt="Logo" />
      )}
    </div>
  );
};
```

## Configuration Management

The application includes a configuration management interface accessible through the settings. This interface allows you to:

- View current configuration values
- Preview configuration JSON
- Test configuration changes (demo mode)
- Reload configuration from server

## Deployment Considerations

### Static Hosting
For static hosting (Netlify, Vercel, etc.):
1. Place `config.json` in the `public/` directory
2. Build and deploy the application
3. The config file will be served at `/config.json`

### Server Hosting
For server hosting:
1. Ensure `config.json` is served from the root path
2. Configure your server to serve the file with appropriate CORS headers
3. Consider implementing a configuration API endpoint for dynamic updates

### Docker Deployment
```dockerfile
# Copy config file to public directory
COPY config.json /app/public/config.json

# Or mount as volume for runtime configuration
VOLUME ["/app/public/config.json"]
```

## Security Considerations

- **Public Access**: The configuration file is publicly accessible
- **Sensitive Data**: Never include sensitive information (API keys, secrets) in the config
- **Validation**: The application validates configuration structure but not content
- **CORS**: Ensure proper CORS configuration if loading from external domains

## Troubleshooting

### Configuration Not Loading
1. Check browser network tab for 404 errors on `/config.json`
2. Verify file is in `public/` directory
3. Check JSON syntax validity
4. Ensure server is serving static files correctly

### Images Not Displaying
1. Verify image URLs are accessible
2. Check CORS headers for external images
3. Ensure image formats are supported by browsers
4. Test image URLs directly in browser

### Favicon Not Updating
1. Clear browser cache
2. Check if multiple favicon links exist
3. Verify favicon file format and size
4. Test in incognito/private browsing mode

## Future Enhancements

Potential future configuration options:
- Theme colors and styling
- Navigation menu structure
- Feature flags
- API endpoints
- Language settings
- Layout preferences
