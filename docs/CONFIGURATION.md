# Application Configuration System

The React application supports dynamic configuration through a `config.json` file that is loaded at runtime. This allows for deployment-time customization without rebuilding the application. Configuration is managed by the development and deployment team only.

## Configuration File Location

The configuration file must be placed at:

```
public/config.json
```

This file is served statically and loaded by the application when it starts.

## Configuration Schema

```json
{
  "ui": {
    "favicon": "string (optional)",
    "appName": "string (optional)"
  },
  "company": {
    "companyLogo": "string (optional)",
    "companyMiniLogo": "string (optional)",
    "companyName": "string (optional)"
  },
  "navigation": {
    "dashboard": {
      "show": "boolean (default: false)"
    },
    "users": {
      "show": "boolean (default: false)",
      "userList": "boolean (default: false)",
      "userRoles": "boolean (default: false)"
    },
    "reports": {
      "show": "boolean (default: false)",
      "salesReport": "boolean (default: false)",
      "userReport": "boolean (default: false)"
    },
    "settings": {
      "show": "boolean (default: false)",
      "general": "boolean (default: false)",
      "notifications": "boolean (default: false)"
    }
  }
}
```

## Configuration Properties

### `ui.favicon`

- **Type**: `string` (URL)
- **Default**: `/favicon-excelfore.ico`
- **Description**: URL to the favicon icon displayed in the browser tab
- **Example**: `"/custom-favicon.ico"` or `"https://example.com/favicon.ico"`

### `ui.appName`

- **Type**: `string`
- **Default**: `"eSync SOTA"`
- **Description**: Application name displayed in the browser tab title
- **Example**: `"My Custom App"`

## Company Configuration

The company section controls branding elements in the application header with a hierarchical fallback system.

### `company.companyLogo`

- **Type**: `string` (URL)
- **Default**: `""` (empty)
- **Description**: Full company logo displayed in the header. **Takes precedence over all other company branding**
- **Example**: `"/company-logo.png"` or `"https://cdn.example.com/logo.svg"`
- **Behavior**: When set, `companyMiniLogo` and `companyName` are ignored

### `company.companyMiniLogo`

- **Type**: `string` (URL)
- **Default**: `""` (empty)
- **Description**: Small logo/icon displayed alongside company name when `companyLogo` is not set
- **Example**: `"/mini-logo.png"` or `"https://cdn.example.com/icon.svg"`
- **Behavior**: Replaces the default avatar with letter "C"

### `company.companyName`

- **Type**: `string`
- **Default**: `""` (empty, shows "Company")
- **Description**: Company name displayed in the header
- **Example**: `"Acme Corporation"` or `"TechStart Solutions"`
- **Behavior**:
  - Replaces "Company" text in header
  - First letter used in avatar when `companyMiniLogo` is not set
  - Ignored when `companyLogo` is set

### Company Branding Logic

1. **Full Logo Priority**: If `companyLogo` is set → Show full logo only
2. **Mini Logo + Name**: If `companyLogo` is empty but `companyMiniLogo` is set → Show mini logo + company name
3. **Name Only**: If both logos are empty but `companyName` is set → Show avatar with first letter + company name
4. **Default Fallback**: If all are empty → Show avatar with "C" + "Company" text

## Navigation Configuration

The navigation section controls which menu items and sub-items are visible in the left navigation drawer. **All navigation items are hidden by default** and must be explicitly enabled.

### Main Navigation Items

#### `navigation.dashboard`

- **Type**: `boolean`
- **Default**: `false`
- **Description**: Shows/hides the Dashboard menu item
- **Path**: `/`

#### `navigation.users`

- **Type**: `boolean`
- **Default**: `false`
- **Description**: Shows/hides the Users menu item and its sub-items
- **Path**: `/users`

#### `navigation.reports`

- **Type**: `boolean`
- **Default**: `false`
- **Description**: Shows/hides the Reports menu item and its sub-items
- **Path**: `/reports`

#### `navigation.settings`

- **Type**: `boolean`
- **Default**: `false`
- **Description**: Shows/hides the Settings menu item and its sub-items
- **Path**: `/settings`

### Sub-Navigation Items

#### Users Sub-Items

- **`navigation.users.userList`**: Shows/hides "User List" sub-item (`/users/list`)
- **`navigation.users.userRoles`**: Shows/hides "User Roles" sub-item (`/users/roles`)

#### Reports Sub-Items

- **`navigation.reports.salesReport`**: Shows/hides "Sales Report" sub-item (`/reports/sales`)
- **`navigation.reports.userReport`**: Shows/hides "User Report" sub-item (`/reports/users`)

#### Settings Sub-Items

- **`navigation.settings.general`**: Shows/hides "General" sub-item (`/settings/general`)
- **`navigation.settings.notifications`**: Shows/hides "Notifications" sub-item (`/settings/notifications`)

### Navigation Rules

1. **Parent Required**: Sub-items are only visible if their parent item is enabled
2. **Default Hidden**: All items are hidden by default (`false`)
3. **Explicit Enable**: Each item must be explicitly set to `true` to be visible
4. **Independent Control**: Each sub-item can be controlled independently

## Example Configurations

### Minimal Configuration (All Hidden)

```json
{
  "ui": {
    "favicon": "",
    "appName": "",
    "logoImageSource": ""
  },
  "navigation": {
    "dashboard": false,
    "users": false,
    "users.userList": false,
    "users.userRoles": false,
    "reports": false,
    "reports.salesReport": false,
    "reports.userReport": false,
    "settings": false,
    "settings.general": false,
    "settings.notifications": false
  }
}
```

### Basic Navigation (Dashboard + Users)

```json
{
  "ui": {
    "favicon": "/custom-favicon.ico",
    "appName": "Acme Corporation Dashboard",
    "logoImageSource": "/acme-logo.png"
  },
  "navigation": {
    "dashboard": true,
    "users": true,
    "users.userList": true,
    "users.userRoles": true,
    "reports": false,
    "reports.salesReport": false,
    "reports.userReport": false,
    "settings": false,
    "settings.general": false,
    "settings.notifications": false
  }
}
```

### Full Navigation (All Enabled)

```json
{
  "ui": {
    "favicon": "/custom-favicon.ico",
    "appName": "Enterprise Portal",
    "logoImageSource": "/enterprise-logo.svg"
  },
  "navigation": {
    "dashboard": true,
    "users": true,
    "users.userList": true,
    "users.userRoles": true,
    "reports": true,
    "reports.salesReport": true,
    "reports.userReport": true,
    "settings": true,
    "settings.general": true,
    "settings.notifications": true
  }
}
```

## How It Works

1. **Application Startup**: The `ConfigProvider` component loads `config.json` from the public directory
2. **Merging**: Configuration values are merged with defaults to ensure all properties exist
3. **Document Updates**: The system automatically updates:
   - Document title (`<title>` tag)
   - Favicon (`<link rel="icon">` tag)
4. **Component Updates**: React components use the `useConfig()` hook to access configuration
5. **Error Handling**: If config loading fails, default values are used

## Usage in Components

```tsx
import { useConfig } from '../contexts/ConfigContext';

const MyComponent: React.FC = () => {
  const { config, loading, error } = useConfig();

  if (loading) return <div>Loading configuration...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h1>{config.ui.appName}</h1>
      {config.ui.logoImageSource && (
        <img src={config.ui.logoImageSource} alt="Logo" />
      )}
    </div>
  );
};
```

## Configuration Management

Configuration is managed exclusively by the development and deployment team through direct modification of the `config.json` file on the server. End users cannot modify the configuration through the application interface.

### For Development Team:

- Edit `config.json` in the `public/` directory during development
- Test changes locally by refreshing the browser
- Commit configuration changes to version control

### For Deployment Team:

- Update `config.json` on the production server
- Changes take effect immediately on page refresh
- No application restart required

## Deployment Considerations

### Static Hosting

For static hosting (Netlify, Vercel, etc.):

1. Place `config.json` in the `public/` directory
2. Build and deploy the application
3. The config file will be served at `/config.json`

### Server Hosting

For server hosting:

1. Ensure `config.json` is served from the root path
2. Configure your server to serve the file with appropriate CORS headers
3. Consider implementing a configuration API endpoint for dynamic updates

### Docker Deployment

```dockerfile
# Copy config file to public directory
COPY config.json /app/public/config.json

# Or mount as volume for runtime configuration
VOLUME ["/app/public/config.json"]
```

## Security Considerations

- **Public Access**: The configuration file is publicly accessible via HTTP
- **Sensitive Data**: Never include sensitive information (API keys, secrets) in the config
- **Team-Only Management**: Configuration can only be modified by development/deployment team with server access
- **No User Interface**: End users cannot modify configuration through the application
- **Validation**: The application validates configuration structure but not content
- **CORS**: Ensure proper CORS configuration if loading from external domains

## Troubleshooting

### Configuration Not Loading

1. Check browser network tab for 404 errors on `/config.json`
2. Verify file is in `public/` directory
3. Check JSON syntax validity
4. Ensure server is serving static files correctly

### Images Not Displaying

1. Verify image URLs are accessible
2. Check CORS headers for external images
3. Ensure image formats are supported by browsers
4. Test image URLs directly in browser

### Favicon Not Updating

1. Clear browser cache
2. Check if multiple favicon links exist
3. Verify favicon file format and size
4. Test in incognito/private browsing mode

## Future Enhancements

Potential future configuration options:

- Theme colors and styling
- Navigation menu structure
- Feature flags
- API endpoints
- Language settings
- Layout preferences
