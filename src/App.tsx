import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from './store';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Users from './pages/Users';
import './i18n';

function App() {
  return (
    <Provider store={store}>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/users" element={<Users />} />
            <Route path="/users/list" element={<div>User List Page</div>} />
            <Route path="/users/roles" element={<div>User Roles Page</div>} />
            <Route path="/reports" element={<div>Reports Page</div>} />
            <Route
              path="/reports/sales"
              element={<div>Sales Report Page</div>}
            />
            <Route
              path="/reports/users"
              element={<div>User Report Page</div>}
            />
            <Route path="/settings" element={<div>Settings Page</div>} />
            <Route
              path="/settings/general"
              element={<div>General Settings Page</div>}
            />
            <Route
              path="/settings/notifications"
              element={<div>Notification Settings Page</div>}
            />
          </Routes>
        </Layout>
      </Router>
    </Provider>
  );
}

export default App;
