import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from './store';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Users from './pages/Users';
import UserList from './pages/UserList';
import UserRoles from './pages/UserRoles';
import Reports from './pages/Reports';
import SalesReport from './pages/SalesReport';
import UserReport from './pages/UserReport';
import Settings from './pages/Settings';
import GeneralSettings from './pages/GeneralSettings';
import NotificationSettings from './pages/NotificationSettings';
import './i18n';

function App() {
  return (
    <Provider store={store}>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/users" element={<Users />} />
            <Route path="/users/list" element={<UserList />} />
            <Route path="/users/roles" element={<UserRoles />} />
            <Route path="/reports" element={<Reports />} />
            <Route path="/reports/sales" element={<SalesReport />} />
            <Route path="/reports/users" element={<UserReport />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/settings/general" element={<GeneralSettings />} />
            <Route
              path="/settings/notifications"
              element={<NotificationSettings />}
            />
          </Routes>
        </Layout>
      </Router>
    </Provider>
  );
}

export default App;
