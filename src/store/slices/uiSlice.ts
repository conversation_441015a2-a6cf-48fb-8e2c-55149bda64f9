import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UIState {
  drawerOpen: boolean;
  drawerExpanded: boolean;
  hasNotifications: boolean;
  notificationCount: number;
}

const initialState: UIState = {
  drawerOpen: true,
  drawerExpanded: true, // Expanded by default as specified
  hasNotifications: false,
  notificationCount: 0,
};

export const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleDrawer: (state) => {
      state.drawerOpen = !state.drawerOpen;
    },
    setDrawerOpen: (state, action: PayloadAction<boolean>) => {
      state.drawerOpen = action.payload;
    },
    toggleDrawerExpanded: (state) => {
      state.drawerExpanded = !state.drawerExpanded;
    },
    setDrawerExpanded: (state, action: PayloadAction<boolean>) => {
      state.drawerExpanded = action.payload;
    },
    setNotifications: (state, action: PayloadAction<{ hasNotifications: boolean; count: number }>) => {
      state.hasNotifications = action.payload.hasNotifications;
      state.notificationCount = action.payload.count;
    },
  },
});

export const { 
  toggleDrawer, 
  setDrawerOpen, 
  toggleDrawerExpanded, 
  setDrawerExpanded, 
  setNotifications 
} = uiSlice.actions;

export default uiSlice.reducer;
