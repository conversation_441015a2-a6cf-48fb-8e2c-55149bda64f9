import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type ThemeMode = 'light' | 'dark' | 'custom';

interface ThemeState {
  mode: ThemeMode;
  customColors?: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
  };
}

const initialState: ThemeState = {
  mode: 'dark', // Default to dark theme as specified
};

export const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setThemeMode: (state, action: PayloadAction<ThemeMode>) => {
      state.mode = action.payload;
    },
    setCustomColors: (state, action: PayloadAction<ThemeState['customColors']>) => {
      state.customColors = action.payload;
    },
  },
});

export const { setThemeMode, setCustomColors } = themeSlice.actions;

export default themeSlice.reducer;
