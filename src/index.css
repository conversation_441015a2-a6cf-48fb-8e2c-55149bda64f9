/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Roboto', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
}

/* Custom Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #94a3b8; /* Light mode: slate-400 */
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #64748b; /* Light mode: slate-500 */
}

::-webkit-scrollbar-track {
  background-color: #f1f5f9; /* Light mode: slate-100 */
  border-radius: 6px;
}

/* Dark mode scrollbar colors */
@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-thumb {
    background-color: #475569; /* Dark mode: slate-600 */
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #64748b; /* Dark mode: slate-500 */
  }

  ::-webkit-scrollbar-track {
    background-color: #1e293b; /* Dark mode: slate-800 */
  }
}

/* Override for Material-UI dark theme when explicitly set */
[data-mui-color-scheme='dark'] ::-webkit-scrollbar-thumb {
  background-color: #475569; /* Dark mode: slate-600 */
}

[data-mui-color-scheme='dark'] ::-webkit-scrollbar-thumb:hover {
  background-color: #64748b; /* Dark mode: slate-500 */
}

[data-mui-color-scheme='dark'] ::-webkit-scrollbar-track {
  background-color: #1e293b; /* Dark mode: slate-800 */
}

/* Override for Material-UI light theme when explicitly set */
[data-mui-color-scheme='light'] ::-webkit-scrollbar-thumb {
  background-color: #94a3b8; /* Light mode: slate-400 */
}

[data-mui-color-scheme='light'] ::-webkit-scrollbar-thumb:hover {
  background-color: #64748b; /* Light mode: slate-500 */
}

[data-mui-color-scheme='light'] ::-webkit-scrollbar-track {
  background-color: #f1f5f9; /* Light mode: slate-100 */
}

/* AG-Grid Theme Customization */
.ag-theme-alpine,
.ag-theme-alpine-dark {
  --ag-font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  --ag-font-size: 14px;
  --ag-header-height: 35px;
  --ag-row-height: 32px;
  --ag-border-radius: 4px;
}

/* Light mode AG-Grid colors */
.ag-theme-alpine {
  --ag-background-color: #ffffff;
  --ag-header-background-color: #f5f5f5;
  --ag-odd-row-background-color: #fafafa;
  --ag-row-hover-color: #e3f2fd;
  --ag-selected-row-background-color: #e1f5fe;
  --ag-border-color: #e0e0e0;
  --ag-foreground-color: #000000;
  --ag-secondary-foreground-color: #666666;
}

/* Dark mode AG-Grid colors */
.ag-theme-alpine-dark {
  --ag-background-color: #121212 !important;
  --ag-header-background-color: #1e1e1e !important;
  --ag-odd-row-background-color: #1a1a1a !important;
  --ag-row-hover-color: #2d2d2d !important;
  --ag-selected-row-background-color: #333333 !important;
  --ag-border-color: #404040 !important;
  --ag-foreground-color: #ffffff !important;
  --ag-secondary-foreground-color: #b0b0b0 !important;
  --ag-header-foreground-color: #ffffff !important;
  --ag-disabled-foreground-color: #666666 !important;
}

/* Material-UI theme integration */
[data-mui-color-scheme='light'] .ag-theme-alpine {
  --ag-background-color: #ffffff;
  --ag-header-background-color: #f5f5f5;
  --ag-odd-row-background-color: #fafafa;
  --ag-row-hover-color: #e3f2fd;
  --ag-selected-row-background-color: #e1f5fe;
  --ag-border-color: #e0e0e0;
  --ag-foreground-color: #000000;
  --ag-secondary-foreground-color: #666666;
}

[data-mui-color-scheme='dark'] .ag-theme-alpine-dark {
  --ag-background-color: #121212 !important;
  --ag-header-background-color: #1e1e1e !important;
  --ag-odd-row-background-color: #1a1a1a !important;
  --ag-row-hover-color: #2d2d2d !important;
  --ag-selected-row-background-color: #333333 !important;
  --ag-border-color: #404040 !important;
  --ag-foreground-color: #ffffff !important;
  --ag-secondary-foreground-color: #b0b0b0 !important;
  --ag-header-foreground-color: #ffffff !important;
  --ag-disabled-foreground-color: #666666 !important;
}

/* AG-Grid styling enhancements */
.ag-theme-alpine .ag-root-wrapper {
  border: 1px solid var(--ag-border-color);
}

.ag-theme-alpine .ag-header {
  background-color: var(--ag-header-background-color);
  border-bottom: 1px solid var(--ag-border-color);
}

.ag-theme-alpine .ag-row {
  border-bottom: 1px solid var(--ag-border-color);
}

.ag-theme-alpine .ag-cell {
  border-right: 1px solid var(--ag-border-color);
  padding: 4px 8px;
}
