import React from 'react';
import { Typography, Paper, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

const NotificationSettings: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        className="font-poppins"
      >
        {t('notifications')}
      </Typography>

      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="body1">
          Notification Settings page content will be displayed here.
        </Typography>
      </Paper>
    </Box>
  );
};

export default NotificationSettings;
