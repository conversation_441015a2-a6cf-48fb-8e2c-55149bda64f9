import React, { useState } from 'react';
import {
  Typography,
  Paper,
  Box,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Divider,
  Grid,
  Card,
  CardContent,
  CardHeader,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useConfig } from '../contexts/ConfigContext';

const ConfigurationSettings: React.FC = () => {
  const { t } = useTranslation();
  const { config, loading, error, reloadConfig } = useConfig();
  const [localConfig, setLocalConfig] = useState(config);
  const [saving, setSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);

  const handleInputChange = (field: keyof typeof config.ui, value: string) => {
    setLocalConfig(prev => ({
      ...prev,
      ui: {
        ...prev.ui,
        [field]: value,
      },
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    setSaveMessage(null);
    
    try {
      // In a real application, you would send this to your backend
      // For now, we'll just show the JSON that would be saved
      const configJson = JSON.stringify(localConfig, null, 2);
      console.log('Configuration to save:', configJson);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSaveMessage('Configuration saved successfully! (Note: This is a demo - changes are not persisted)');
    } catch (err) {
      setSaveMessage('Error saving configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleReload = async () => {
    await reloadConfig();
    setLocalConfig(config);
    setSaveMessage('Configuration reloaded from server');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom className="font-poppins">
        Configuration Settings
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          Error loading configuration: {error}
        </Alert>
      )}

      {saveMessage && (
        <Alert 
          severity={saveMessage.includes('Error') ? 'error' : 'success'} 
          sx={{ mb: 3 }}
          onClose={() => setSaveMessage(null)}
        >
          {saveMessage}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Configuration Form */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader title="UI Configuration" />
            <CardContent>
              <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                <TextField
                  label="Application Name"
                  value={localConfig.ui.appName}
                  onChange={(e) => handleInputChange('appName', e.target.value)}
                  fullWidth
                  helperText="The name displayed in the browser tab. Leave empty to use default 'eSync SOTA'"
                />

                <TextField
                  label="Favicon URL"
                  value={localConfig.ui.favicon}
                  onChange={(e) => handleInputChange('favicon', e.target.value)}
                  fullWidth
                  helperText="URL to the favicon icon. Leave empty to use default favicon"
                />

                <TextField
                  label="Logo Image URL"
                  value={localConfig.ui.logoImageSource}
                  onChange={(e) => handleInputChange('logoImageSource', e.target.value)}
                  fullWidth
                  helperText="URL to the logo image displayed in the header. Leave empty to show default 'C' avatar and 'Company' text"
                />

                <Divider />

                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    onClick={handleSave}
                    disabled={saving}
                    startIcon={saving ? <CircularProgress size={20} /> : null}
                  >
                    {saving ? 'Saving...' : 'Save Configuration'}
                  </Button>

                  <Button
                    variant="outlined"
                    onClick={handleReload}
                    disabled={saving}
                  >
                    Reload from Server
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Preview */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Current Configuration" />
            <CardContent>
              <Typography variant="subtitle2" gutterBottom>
                config.json Preview:
              </Typography>
              <Paper
                sx={{
                  p: 2,
                  backgroundColor: 'grey.100',
                  fontFamily: 'monospace',
                  fontSize: '0.875rem',
                  overflow: 'auto',
                  maxHeight: '300px',
                }}
              >
                <pre>{JSON.stringify(localConfig, null, 2)}</pre>
              </Paper>

              <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
                Current Values:
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Typography variant="body2">
                  <strong>App Name:</strong> {localConfig.ui.appName || 'eSync SOTA (default)'}
                </Typography>
                <Typography variant="body2">
                  <strong>Favicon:</strong> {localConfig.ui.favicon || '/favicon-excelfore.ico (default)'}
                </Typography>
                <Typography variant="body2">
                  <strong>Logo:</strong> {localConfig.ui.logoImageSource || 'Default avatar (C) + Company text'}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ConfigurationSettings;
