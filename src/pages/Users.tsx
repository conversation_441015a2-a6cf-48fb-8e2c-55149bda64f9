import React from 'react';
import { Typography, Paper, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

const Users: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        className="font-poppins"
      >
        {t('users')}
      </Typography>

      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="body1">{t('usersContent')}</Typography>
      </Paper>
    </Box>
  );
};

export default Users;
