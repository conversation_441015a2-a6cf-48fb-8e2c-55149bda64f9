import React from 'react';
import { Typography, Paper, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

const Reports: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        className="font-poppins"
      >
        {t('reports')}
      </Typography>

      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="body1">{t('reportsContent')}</Typography>
      </Paper>
    </Box>
  );
};

export default Reports;
