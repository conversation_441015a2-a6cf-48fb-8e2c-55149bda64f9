import React from 'react';
import { Typography, Paper, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

const SalesReport: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        className="font-poppins"
      >
        {t('salesReport')}
      </Typography>

      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="body1">{t('salesReportContent')}</Typography>
      </Paper>
    </Box>
  );
};

export default SalesReport;
