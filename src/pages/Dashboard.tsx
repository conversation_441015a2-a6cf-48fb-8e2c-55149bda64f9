import React, { useState } from 'react';
import { Typo<PERSON>, Box, Grid, Button, Chip, Stack } from '@mui/material';
import { useTranslation } from 'react-i18next';
import ResizableCard from '../components/ResizableCard';
import DataGrid from '../components/DataGrid';

// Define card data interface
interface DashboardCard {
  id: string;
  title: string;
  content: React.ReactNode;
}

const Dashboard: React.FC = () => {
  const { t } = useTranslation();

  // Define initial card order
  const [cards, setCards] = useState<DashboardCard[]>([
    {
      id: 'welcome',
      title: t('welcomeCard'),
      content: t('dashboardContent'),
    },
    {
      id: 'statistics',
      title: t('statistics'),
      content: (
        <Box>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {t('statisticsContent')}
          </Typography>
          <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
            <Chip label="Users: 1,234" color="primary" size="small" />
            <Chip label="Active: 987" color="success" size="small" />
          </Stack>
        </Box>
      ),
    },
    {
      id: 'quickActions',
      title: t('quickActions'),
      content: (
        <Box>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {t('quickActionsContent')}
          </Typography>
          <Stack spacing={1} sx={{ mt: 2 }}>
            <Button variant="contained" size="small" fullWidth>
              Add User
            </Button>
            <Button variant="outlined" size="small" fullWidth>
              Generate Report
            </Button>
            <Button variant="outlined" size="small" fullWidth>
              Export Data
            </Button>
          </Stack>
        </Box>
      ),
    },
    {
      id: 'dataTable',
      title: 'Data Table',
      content: (
        <DataGrid
          rowData={[
            {
              id: 1,
              name: 'John Doe',
              email: '<EMAIL>',
              role: 'Admin',
              status: 'Active',
            },
            {
              id: 2,
              name: 'Jane Smith',
              email: '<EMAIL>',
              role: 'User',
              status: 'Active',
            },
            {
              id: 3,
              name: 'Bob Johnson',
              email: '<EMAIL>',
              role: 'Manager',
              status: 'Inactive',
            },
            {
              id: 4,
              name: 'Alice Brown',
              email: '<EMAIL>',
              role: 'User',
              status: 'Active',
            },
            {
              id: 5,
              name: 'Charlie Wilson',
              email: '<EMAIL>',
              role: 'Admin',
              status: 'Pending',
            },
          ]}
          columnDefs={[
            { field: 'id', headerName: 'ID', width: 60 },
            { field: 'name', headerName: 'Name', width: 100 },
            { field: 'email', headerName: 'Email', width: 150 },
            { field: 'role', headerName: 'Role', width: 80 },
            { field: 'status', headerName: 'Status', width: 80 },
          ]}
          rowSelection="single"
          enableSorting={true}
          enableFiltering={true}
          enableResizing={true}
          animateRows={true}
        />
      ),
    },
  ]);

  const [draggedCard, setDraggedCard] = useState<string | null>(null);

  const handleDragStart = (e: React.DragEvent, cardId: string) => {
    setDraggedCard(cardId);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', cardId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetCardId: string) => {
    e.preventDefault();

    if (!draggedCard || draggedCard === targetCardId) return;

    const draggedIndex = cards.findIndex(card => card.id === draggedCard);
    const targetIndex = cards.findIndex(card => card.id === targetCardId);

    if (draggedIndex === -1 || targetIndex === -1) return;

    const newCards = [...cards];
    const [draggedItem] = newCards.splice(draggedIndex, 1);
    newCards.splice(targetIndex, 0, draggedItem);

    setCards(newCards);
    setDraggedCard(null);
  };

  const handleDragEnd = () => {
    setDraggedCard(null);
  };

  return (
    <Box>
      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        className="font-poppins"
      >
        {t('dashboard')}
      </Typography>

      <Grid container spacing={3}>
        {cards.map(card => (
          <Grid item xs={12} md={6} lg={4} key={card.id}>
            <ResizableCard
              title={card.title}
              draggable={true}
              onDragStart={e => handleDragStart(e, card.id)}
              onDragOver={handleDragOver}
              onDrop={e => handleDrop(e, card.id)}
              onDragEnd={handleDragEnd}
              isDragging={draggedCard === card.id}
            >
              {card.content}
            </ResizableCard>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default Dashboard;
