import React from 'react';
import { Typo<PERSON>, <PERSON>, Grid, Button, Chip, Stack } from '@mui/material';
import { useTranslation } from 'react-i18next';
import ResizableCard from '../components/ResizableCard';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        className="font-poppins"
      >
        {t('dashboard')}
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6} lg={4}>
          <ResizableCard title={t('welcomeCard')}>
            {t('dashboardContent')}
          </ResizableCard>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <ResizableCard title={t('statistics')}>
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t('statisticsContent')}
              </Typography>
              <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
                <Chip label="Users: 1,234" color="primary" size="small" />
                <Chip label="Active: 987" color="success" size="small" />
              </Stack>
            </Box>
          </ResizableCard>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <ResizableCard title={t('quickActions')}>
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t('quickActionsContent')}
              </Typography>
              <Stack spacing={1} sx={{ mt: 2 }}>
                <Button variant="contained" size="small" fullWidth>
                  Add User
                </Button>
                <Button variant="outlined" size="small" fullWidth>
                  Generate Report
                </Button>
              </Stack>
            </Box>
          </ResizableCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
