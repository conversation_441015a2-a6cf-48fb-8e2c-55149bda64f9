import React from 'react';
import { Typography, Paper, Box, Grid } from '@mui/material';
import { useTranslation } from 'react-i18next';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        className="font-poppins"
      >
        {t('dashboard')}
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6} lg={4}>
          <Paper
            elevation={2}
            sx={{
              p: 3,
              resize: 'both',
              overflow: 'auto',
              minWidth: '250px',
              minHeight: '150px',
              maxWidth: '100%',
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: 0,
                right: 0,
                width: '20px',
                height: '20px',
                background:
                  'linear-gradient(-45deg, transparent 0%, transparent 43%, currentColor 45%, currentColor 55%, transparent 57%, transparent 100%)',
                opacity: 0.3,
                pointerEvents: 'none',
              },
              '&:hover::after': {
                opacity: 0.6,
              },
            }}
          >
            <Typography variant="h6" gutterBottom>
              {t('welcomeCard')}
            </Typography>
            <Typography variant="body1">{t('dashboardContent')}</Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <Paper
            elevation={2}
            sx={{
              p: 3,
              resize: 'both',
              overflow: 'auto',
              minWidth: '250px',
              minHeight: '150px',
              maxWidth: '100%',
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: 0,
                right: 0,
                width: '20px',
                height: '20px',
                background:
                  'linear-gradient(-45deg, transparent 0%, transparent 43%, currentColor 45%, currentColor 55%, transparent 57%, transparent 100%)',
                opacity: 0.3,
                pointerEvents: 'none',
              },
              '&:hover::after': {
                opacity: 0.6,
              },
            }}
          >
            <Typography variant="h6" gutterBottom>
              {t('statistics')}
            </Typography>
            <Typography variant="body1">{t('statisticsContent')}</Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <Paper
            elevation={2}
            sx={{
              p: 3,
              resize: 'both',
              overflow: 'auto',
              minWidth: '250px',
              minHeight: '150px',
              maxWidth: '100%',
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: 0,
                right: 0,
                width: '20px',
                height: '20px',
                background:
                  'linear-gradient(-45deg, transparent 0%, transparent 43%, currentColor 45%, currentColor 55%, transparent 57%, transparent 100%)',
                opacity: 0.3,
                pointerEvents: 'none',
              },
              '&:hover::after': {
                opacity: 0.6,
              },
            }}
          >
            <Typography variant="h6" gutterBottom>
              {t('quickActions')}
            </Typography>
            <Typography variant="body1">{t('quickActionsContent')}</Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
