import React from 'react';
import { Typography, Paper, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

const UserReport: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        className="font-poppins"
      >
        {t('userReport')}
      </Typography>

      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="body1">{t('userReportContent')}</Typography>
      </Paper>
    </Box>
  );
};

export default UserReport;
