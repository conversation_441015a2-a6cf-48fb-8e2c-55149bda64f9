import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import DataGrid from '../components/DataGrid';

/**
 * Examples of how to use the DataGrid component
 */
const DataGridExamples: React.FC = () => {
  // Example 1: Simple user data
  const userData = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'Active' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'User', status: 'Active' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Manager', status: 'Inactive' },
  ];

  const userColumns = [
    { field: 'id', headerName: 'ID', width: 60 },
    { field: 'name', headerName: 'Name', width: 120 },
    { field: 'email', headerName: 'Email', width: 200 },
    { field: 'role', headerName: 'Role', width: 100 },
    { field: 'status', headerName: 'Status', width: 100 },
  ];

  // Example 2: Product data with numbers
  const productData = [
    { id: 'P001', name: 'Laptop', category: 'Electronics', price: 999.99, stock: 25 },
    { id: 'P002', name: 'Mouse', category: 'Electronics', price: 29.99, stock: 150 },
    { id: 'P003', name: 'Keyboard', category: 'Electronics', price: 79.99, stock: 75 },
  ];

  const productColumns = [
    { field: 'id', headerName: 'Product ID', width: 100 },
    { field: 'name', headerName: 'Product Name', width: 150 },
    { field: 'category', headerName: 'Category', width: 120 },
    { 
      field: 'price', 
      headerName: 'Price', 
      width: 100,
      valueFormatter: (params: any) => `$${params.value.toFixed(2)}`
    },
    { field: 'stock', headerName: 'Stock', width: 80 },
  ];

  const handleRowSelected = (event: any) => {
    console.log('Row selected:', event.data);
  };

  const handleCellClicked = (event: any) => {
    console.log('Cell clicked:', event.colDef.field, event.value);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        DataGrid Component Examples
      </Typography>

      {/* Example 1: Basic User Table */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Example 1: User Management Table
        </Typography>
        <DataGrid
          rowData={userData}
          columnDefs={userColumns}
          height="200px"
          enableSorting={true}
          enableFiltering={true}
          enableResizing={true}
          rowSelection="single"
          onRowSelected={handleRowSelected}
          onCellClicked={handleCellClicked}
        />
      </Paper>

      {/* Example 2: Product Table with Custom Formatting */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Example 2: Product Inventory Table
        </Typography>
        <DataGrid
          rowData={productData}
          columnDefs={productColumns}
          height="200px"
          enableSorting={true}
          enableFiltering={false}
          enableResizing={true}
          rowSelection="multiple"
          animateRows={true}
        />
      </Paper>

      {/* Example 3: Compact Table */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Example 3: Compact View (150px height)
        </Typography>
        <DataGrid
          rowData={userData.slice(0, 2)}
          columnDefs={[
            { field: 'name', headerName: 'Name', width: 120 },
            { field: 'email', headerName: 'Email', width: 200 },
            { field: 'status', headerName: 'Status', width: 100 },
          ]}
          height="150px"
          enableSorting={false}
          enableFiltering={false}
          enableResizing={false}
          animateRows={false}
        />
      </Paper>
    </Box>
  );
};

export default DataGridExamples;
