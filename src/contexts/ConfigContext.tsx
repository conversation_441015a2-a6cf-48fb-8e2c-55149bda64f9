import React, { createContext, useContext, useEffect, useState } from 'react';

// Configuration interface
export interface AppConfig {
  ui: {
    favicon: string;
    appName: string;
    logoImageSource: string;
  };
}

// Default configuration
const defaultConfig: AppConfig = {
  ui: {
    favicon: '/favicon-excelfore.ico',
    appName: 'eSync SOTA',
    logoImageSource: '',
  },
};

// Context interface
interface ConfigContextType {
  config: AppConfig;
  loading: boolean;
  error: string | null;
}

// Create context
const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

// Custom hook to use config
export const useConfig = (): ConfigContextType => {
  const context = useContext(ConfigContext);
  if (!context) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
};

// Config provider component
export const ConfigProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [config, setConfig] = useState<AppConfig>(defaultConfig);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadConfig = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      // Fetch config.json from public directory
      const response = await fetch('/config.json');

      if (!response.ok) {
        throw new Error(`Failed to load config: ${response.status}`);
      }

      const configData = await response.json();

      // Merge with default config to ensure all properties exist
      const mergedConfig: AppConfig = {
        ui: {
          favicon: configData.ui?.favicon || defaultConfig.ui.favicon,
          appName: configData.ui?.appName || defaultConfig.ui.appName,
          logoImageSource:
            configData.ui?.logoImageSource || defaultConfig.ui.logoImageSource,
        },
      };

      setConfig(mergedConfig);

      // Apply configuration to document
      applyConfigToDocument(mergedConfig);
    } catch (err) {
      console.error('Error loading config:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      // Use default config on error
      setConfig(defaultConfig);
      applyConfigToDocument(defaultConfig);
    } finally {
      setLoading(false);
    }
  };

  const applyConfigToDocument = (config: AppConfig): void => {
    // Update document title
    if (config.ui.appName) {
      document.title = config.ui.appName;
    }

    // Update favicon
    if (config.ui.favicon) {
      // Remove existing favicon links
      const existingFavicons = document.querySelectorAll('link[rel*="icon"]');
      existingFavicons.forEach(link => link.remove());

      // Add new favicon
      const faviconLink = document.createElement('link');
      faviconLink.rel = 'icon';
      faviconLink.type = 'image/x-icon';
      faviconLink.href = config.ui.favicon;
      document.head.appendChild(faviconLink);
    }
  };

  useEffect(() => {
    loadConfig();
  }, []);

  const value: ConfigContextType = {
    config,
    loading,
    error,
  };

  return (
    <ConfigContext.Provider value={value}>{children}</ConfigContext.Provider>
  );
};

export default ConfigContext;
