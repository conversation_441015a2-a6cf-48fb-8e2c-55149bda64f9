import React, { createContext, useContext, useEffect, useState } from 'react';

// Configuration interface
export interface AppConfig {
  ui: {
    favicon: string;
    appName: string;
  };
  company: {
    companyLogo: string;
    companyMiniLogo: string;
    companyName: string;
  };
  navigation: {
    dashboard: {
      show: boolean;
    };
    users: {
      show: boolean;
      userList: boolean;
      userRoles: boolean;
    };
    reports: {
      show: boolean;
      salesReport: boolean;
      userReport: boolean;
    };
    settings: {
      show: boolean;
      general: boolean;
      notifications: boolean;
    };
  };
}

// Default configuration
const defaultConfig: AppConfig = {
  ui: {
    favicon: '/favicon-excelfore.ico',
    appName: 'eSync SOTA',
  },
  company: {
    companyLogo: '',
    companyMiniLogo: '',
    companyName: '',
  },
  navigation: {
    dashboard: {
      show: true,
    },
    users: {
      show: false,
      userList: false,
      userRoles: false,
    },
    reports: {
      show: false,
      salesReport: false,
      userReport: false,
    },
    settings: {
      show: false,
      general: false,
      notifications: false,
    },
  },
};

// Context interface
interface ConfigContextType {
  config: AppConfig;
  loading: boolean;
  error: string | null;
}

// Create context
const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

// Custom hook to use config
export const useConfig = (): ConfigContextType => {
  const context = useContext(ConfigContext);
  if (!context) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
};

// Config provider component
export const ConfigProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [config, setConfig] = useState<AppConfig>(defaultConfig);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadConfig = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      // Fetch config.json from public directory
      const response = await fetch('/config.json');

      if (!response.ok) {
        throw new Error(`Failed to load config: ${response.status}`);
      }

      const configData = await response.json();

      // Merge with default config to ensure all properties exist
      const mergedConfig: AppConfig = {
        ui: {
          favicon: configData.ui?.favicon || defaultConfig.ui.favicon,
          appName: configData.ui?.appName || defaultConfig.ui.appName,
        },
        company: {
          companyLogo:
            configData.company?.companyLogo ||
            defaultConfig.company.companyLogo,
          companyMiniLogo:
            configData.company?.companyMiniLogo ||
            defaultConfig.company.companyMiniLogo,
          companyName:
            configData.company?.companyName ||
            defaultConfig.company.companyName,
        },
        navigation: {
          dashboard: {
            show:
              configData.navigation?.dashboard?.show ??
              defaultConfig.navigation.dashboard.show,
          },
          users: {
            show:
              configData.navigation?.users?.show ??
              defaultConfig.navigation.users.show,
            userList:
              configData.navigation?.users?.userList ??
              defaultConfig.navigation.users.userList,
            userRoles:
              configData.navigation?.users?.userRoles ??
              defaultConfig.navigation.users.userRoles,
          },
          reports: {
            show:
              configData.navigation?.reports?.show ??
              defaultConfig.navigation.reports.show,
            salesReport:
              configData.navigation?.reports?.salesReport ??
              defaultConfig.navigation.reports.salesReport,
            userReport:
              configData.navigation?.reports?.userReport ??
              defaultConfig.navigation.reports.userReport,
          },
          settings: {
            show:
              configData.navigation?.settings?.show ??
              defaultConfig.navigation.settings.show,
            general:
              configData.navigation?.settings?.general ??
              defaultConfig.navigation.settings.general,
            notifications:
              configData.navigation?.settings?.notifications ??
              defaultConfig.navigation.settings.notifications,
          },
        },
      };

      setConfig(mergedConfig);

      // Apply configuration to document
      applyConfigToDocument(mergedConfig);
    } catch (err) {
      console.error('Error loading config:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      // Use default config on error
      setConfig(defaultConfig);
      applyConfigToDocument(defaultConfig);
    } finally {
      setLoading(false);
    }
  };

  const applyConfigToDocument = (config: AppConfig): void => {
    // Update document title
    if (config.ui.appName) {
      document.title = config.ui.appName;
    }

    // Update favicon
    if (config.ui.favicon) {
      // Remove existing favicon links
      const existingFavicons = document.querySelectorAll('link[rel*="icon"]');
      existingFavicons.forEach(link => link.remove());

      // Add new favicon
      const faviconLink = document.createElement('link');
      faviconLink.rel = 'icon';
      faviconLink.type = 'image/x-icon';
      faviconLink.href = config.ui.favicon;
      document.head.appendChild(faviconLink);
    }
  };

  useEffect(() => {
    loadConfig();
  }, []);

  const value: ConfigContextType = {
    config,
    loading,
    error,
  };

  return (
    <ConfigContext.Provider value={value}>{children}</ConfigContext.Provider>
  );
};

export default ConfigContext;
