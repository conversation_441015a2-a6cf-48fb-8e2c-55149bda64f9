import React from 'react';
import { Paper, Typography } from '@mui/material';
import type { SxProps, Theme } from '@mui/system';

interface ResizableCardProps {
  title: string;
  children: React.ReactNode;
  elevation?: number;
  minWidth?: string;
  minHeight?: string;
  maxWidth?: string;
  sx?: SxProps<Theme>;
}

const ResizableCard: React.FC<ResizableCardProps> = ({
  title,
  children,
  elevation = 2,
  minWidth = '250px',
  minHeight = '150px',
  maxWidth = '100%',
  sx = {},
}) => {
  return (
    <Paper
      elevation={elevation}
      sx={{
        p: 3,
        resize: 'both',
        overflow: 'auto',
        minWidth,
        minHeight,
        maxWidth,
        position: 'relative',
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: 0,
          right: 0,
          width: '20px',
          height: '20px',
          background:
            'linear-gradient(-45deg, transparent 0%, transparent 43%, currentColor 45%, currentColor 55%, transparent 57%, transparent 100%)',
          opacity: 0.3,
          pointerEvents: 'none',
        },
        '&:hover::after': {
          opacity: 0.6,
        },
        ...sx,
      }}
    >
      <Typography variant="h6" gutterBottom>
        {title}
      </Typography>
      {typeof children === 'string' ? (
        <Typography variant="body1">{children}</Typography>
      ) : (
        children
      )}
    </Paper>
  );
};

export default ResizableCard;
