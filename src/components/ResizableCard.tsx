import React from 'react';
import { Paper, Typography, Box } from '@mui/material';
import { DragIndicator } from '@mui/icons-material';
import type { SxProps, Theme } from '@mui/system';

interface ResizableCardProps {
  title: string;
  children: React.ReactNode;
  elevation?: number;
  minWidth?: string;
  minHeight?: string;
  maxWidth?: string;
  sx?: SxProps<Theme>;
  draggable?: boolean;
  onDragStart?: (e: React.DragEvent) => void;
  onDragOver?: (e: React.DragEvent) => void;
  onDrop?: (e: React.DragEvent) => void;
  onDragEnd?: () => void;
  isDragging?: boolean;
}

const ResizableCard: React.FC<ResizableCardProps> = ({
  title,
  children,
  elevation = 2,
  minWidth = '250px',
  minHeight = '150px',
  maxWidth = '100%',
  sx = {},
  draggable = false,
  onDragStart,
  onDragOver,
  onDrop,
  onDragEnd,
  isDragging = false,
}) => {
  return (
    <Paper
      elevation={elevation}
      draggable={draggable}
      onDragStart={onDragStart}
      onDragOver={onDragOver}
      onDrop={onDrop}
      onDragEnd={onDragEnd}
      sx={{
        p: 3,
        resize: 'both',
        overflow: 'auto',
        minWidth,
        minHeight,
        maxWidth,
        position: 'relative',
        opacity: isDragging ? 0.5 : 1,
        transform: isDragging ? 'rotate(5deg)' : 'none',
        transition: 'opacity 0.2s ease, transform 0.2s ease',
        cursor: draggable ? 'move' : 'default',
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: 0,
          right: 0,
          width: '20px',
          height: '20px',
          background:
            'linear-gradient(-45deg, transparent 81%, transparent 100%)',
          opacity: 0.3,
          pointerEvents: 'none',
        },
        '&:hover::after': {
          opacity: 0.6,
        },
        ...sx,
      }}
    >
      {/* Header with drag handle */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          mb: 1,
          cursor: draggable ? 'grab' : 'default',
          '&:active': {
            cursor: draggable ? 'grabbing' : 'default',
          },
        }}
      >
        {/* {draggable && (
          <DragIndicator
            sx={{
              mr: 1,
              color: 'text.secondary',
              fontSize: '1.2rem',
              opacity: 0.6,
              '&:hover': {
                opacity: 1,
              },
            }}
          />
        )} */}
        <Typography variant="h6" sx={{ flexGrow: 1 }}>
          {title}
        </Typography>
      </Box>

      {/* Content */}
      <Box sx={{ cursor: 'default' }}>
        {typeof children === 'string' ? (
          <Typography variant="body1">{children}</Typography>
        ) : (
          children
        )}
      </Box>
    </Paper>
  );
};

export default ResizableCard;
