import React, { useState } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Box,
  Tooltip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  Assessment as ReportsIcon,
  ExpandLess,
  ExpandMore,
  Person as PersonIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  TrendingUp as SalesIcon,
  Analytics as UserReportIcon,
  Tune as GeneralIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppSelector } from '../hooks/redux';
import { useConfig } from '../contexts/ConfigContext';

interface SubMenuItem {
  id: string;
  label: string;
  path: string;
  icon?: React.ReactNode;
}

interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ReactNode;
  subItems?: SubMenuItem[];
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'dashboard',
    path: '/',
    icon: <DashboardIcon />,
  },
  {
    id: 'users',
    label: 'users',
    path: '/users',
    icon: <PeopleIcon />,
    subItems: [
      {
        id: 'user-list',
        label: 'userList',
        path: '/users/list',
        icon: <PersonIcon />,
      },
      {
        id: 'user-roles',
        label: 'userRoles',
        path: '/users/roles',
        icon: <SecurityIcon />,
      },
    ],
  },
  {
    id: 'reports',
    label: 'reports',
    path: '/reports',
    icon: <ReportsIcon />,
    subItems: [
      {
        id: 'sales-report',
        label: 'salesReport',
        path: '/reports/sales',
        icon: <SalesIcon />,
      },
      {
        id: 'user-report',
        label: 'userReport',
        path: '/reports/users',
        icon: <UserReportIcon />,
      },
    ],
  },
  {
    id: 'settings',
    label: 'settings',
    path: '/settings',
    icon: <SettingsIcon />,
    subItems: [
      {
        id: 'general-settings',
        label: 'general',
        path: '/settings/general',
        icon: <GeneralIcon />,
      },
      {
        id: 'notification-settings',
        label: 'notifications',
        path: '/settings/notifications',
        icon: <NotificationsIcon />,
      },
    ],
  },
];

const DRAWER_WIDTH = 280;
const DRAWER_WIDTH_COLLAPSED = 64;

const LeftNavigationDrawer: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { config } = useConfig();

  const { drawerExpanded } = useAppSelector(state => state.ui);
  const { mode: themeMode } = useAppSelector(state => state.theme);

  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  // Filter navigation items based on configuration
  const getVisibleNavigationItems = () => {
    return navigationItems
      .filter(item => {
        // Check if main item is enabled
        const navigationSection =
          config.navigation[item.id as keyof typeof config.navigation];
        if (
          !navigationSection ||
          !('show' in navigationSection) ||
          !navigationSection.show
        ) {
          return false;
        }
        return true;
      })
      .map(item => {
        // Handle sub-items filtering for items that passed the main filter
        if (item.subItems) {
          const visibleSubItems = item.subItems.filter(subItem => {
            // Map sub-item IDs to config properties
            if (item.id === 'users') {
              if (subItem.id === 'user-list')
                return config.navigation.users.userList;
              if (subItem.id === 'user-roles')
                return config.navigation.users.userRoles;
            } else if (item.id === 'reports') {
              if (subItem.id === 'sales-report')
                return config.navigation.reports.salesReport;
              if (subItem.id === 'user-report')
                return config.navigation.reports.userReport;
            } else if (item.id === 'settings') {
              if (subItem.id === 'general-settings')
                return config.navigation.settings.general;
              if (subItem.id === 'notification-settings')
                return config.navigation.settings.notifications;
            }
            return false;
          });

          return {
            ...item,
            subItems: visibleSubItems,
          };
        }
        return item;
      });
  };

  const visibleNavigationItems = getVisibleNavigationItems();

  const handleItemClick = (item: NavigationItem) => {
    if (item.subItems && item.subItems.length > 0) {
      // Toggle expansion for items with sub-items
      setExpandedItems(prev =>
        prev.includes(item.id)
          ? prev.filter(id => id !== item.id)
          : [...prev, item.id]
      );
    } else {
      // Navigate for items without sub-items
      navigate(item.path);
    }
  };

  const handleSubItemClick = (subItem: SubMenuItem) => {
    navigate(subItem.path);
  };

  const isItemActive = (path: string) => {
    return location.pathname === path;
  };

  const isParentActive = (item: NavigationItem) => {
    if (isItemActive(item.path)) return true;
    if (item.subItems) {
      return item.subItems.some(subItem => isItemActive(subItem.path));
    }
    return false;
  };

  const drawerWidth = drawerExpanded ? DRAWER_WIDTH : DRAWER_WIDTH_COLLAPSED;

  return (
    <Drawer
      variant="persistent"
      anchor="left"
      open={true}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          backgroundColor: themeMode === 'dark' ? '#0f172a' : '#f8fafc',
          borderRight: `1px solid ${themeMode === 'dark' ? '#334155' : '#e2e8f0'}`,
          transition: 'width 0.3s ease',
          marginTop: '64px', // Account for header height
        },
      }}
    >
      <Box sx={{ overflow: 'auto', height: '100%' }}>
        <List disablePadding>
          {visibleNavigationItems.map(item => {
            const isExpanded = expandedItems.includes(item.id);
            const isActive = isParentActive(item);
            const hasSubItems = item.subItems && item.subItems.length > 0;

            return (
              <React.Fragment key={item.id}>
                <ListItem disablePadding>
                  <Tooltip
                    title={drawerExpanded ? '' : t(item.label)}
                    placement="right"
                    arrow
                  >
                    <ListItemButton
                      onClick={() => handleItemClick(item)}
                      selected={isActive}
                      sx={{
                        minHeight: 48,
                        justifyContent: drawerExpanded ? 'initial' : 'center',
                        px: 2.5,
                        backgroundColor: isActive
                          ? themeMode === 'dark'
                            ? '#1e40af'
                            : '#3b82f6'
                          : 'transparent',
                        '&:hover': {
                          backgroundColor:
                            themeMode === 'dark' ? '#1e293b' : '#f1f5f9',
                        },
                        '&.Mui-selected': {
                          backgroundColor: isActive
                            ? themeMode === 'dark'
                              ? '#1e40af'
                              : '#3b82f6'
                            : 'transparent',
                          '&:hover': {
                            backgroundColor: isActive
                              ? themeMode === 'dark'
                                ? '#1d4ed8'
                                : '#2563eb'
                              : themeMode === 'dark'
                                ? '#1e293b'
                                : '#f1f5f9',
                          },
                        },
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          minWidth: 0,
                          mr: drawerExpanded ? 3 : 'auto',
                          justifyContent: 'center',
                          color: isActive ? 'white' : 'inherit',
                        }}
                      >
                        {item.icon}
                      </ListItemIcon>

                      {drawerExpanded && (
                        <>
                          <ListItemText
                            primary={t(item.label)}
                            sx={{
                              color: isActive ? 'white' : 'inherit',
                            }}
                          />
                          {hasSubItems &&
                            (isExpanded ? <ExpandLess /> : <ExpandMore />)}
                        </>
                      )}
                    </ListItemButton>
                  </Tooltip>
                </ListItem>

                {/* Sub-items */}
                {hasSubItems && (
                  <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                    <List component="div" disablePadding>
                      {item.subItems!.map(subItem => {
                        const isSubItemActive = isItemActive(subItem.path);

                        return (
                          <ListItem key={subItem.id} disablePadding>
                            {drawerExpanded ? (
                              // Expanded view - show full sub-item with text
                              <ListItemButton
                                onClick={() => handleSubItemClick(subItem)}
                                selected={isSubItemActive}
                                sx={{
                                  pl: 4,
                                  minHeight: 40,
                                  backgroundColor: isSubItemActive
                                    ? themeMode === 'dark'
                                      ? '#1e40af'
                                      : '#3b82f6'
                                    : 'transparent',
                                  '&:hover': {
                                    backgroundColor:
                                      themeMode === 'dark'
                                        ? '#1e293b'
                                        : '#f1f5f9',
                                  },
                                  '&.Mui-selected': {
                                    backgroundColor: isSubItemActive
                                      ? themeMode === 'dark'
                                        ? '#1e40af'
                                        : '#3b82f6'
                                      : 'transparent',
                                    '&:hover': {
                                      backgroundColor: isSubItemActive
                                        ? themeMode === 'dark'
                                          ? '#1d4ed8'
                                          : '#2563eb'
                                        : themeMode === 'dark'
                                          ? '#1e293b'
                                          : '#f1f5f9',
                                    },
                                  },
                                }}
                              >
                                {subItem.icon && (
                                  <ListItemIcon
                                    sx={{
                                      minWidth: 0,
                                      mr: 2,
                                      color: isSubItemActive
                                        ? 'white'
                                        : 'inherit',
                                    }}
                                  >
                                    {subItem.icon}
                                  </ListItemIcon>
                                )}
                                <ListItemText
                                  primary={t(subItem.label)}
                                  sx={{
                                    color: isSubItemActive
                                      ? 'white'
                                      : 'inherit',
                                  }}
                                />
                              </ListItemButton>
                            ) : (
                              // Collapsed view - show only icon with tooltip and connection line
                              <Box sx={{ position: 'relative', width: '100%' }}>
                                {/* Connection line to parent */}
                                <Box
                                  sx={{
                                    position: 'absolute',
                                    left: 16,
                                    top: 0,
                                    bottom: 0,
                                    width: 2,
                                    backgroundColor:
                                      themeMode === 'dark'
                                        ? '#475569'
                                        : '#cbd5e1',
                                  }}
                                />
                                <Tooltip
                                  title={t(subItem.label)}
                                  placement="right"
                                  arrow
                                >
                                  <ListItemButton
                                    onClick={() => handleSubItemClick(subItem)}
                                    selected={isSubItemActive}
                                    sx={{
                                      minHeight: 40,
                                      justifyContent: 'center',
                                      px: 2.5,
                                      backgroundColor: isSubItemActive
                                        ? themeMode === 'dark'
                                          ? '#1e40af'
                                          : '#3b82f6'
                                        : 'transparent',
                                      '&:hover': {
                                        backgroundColor:
                                          themeMode === 'dark'
                                            ? '#1e293b'
                                            : '#f1f5f9',
                                      },
                                      '&.Mui-selected': {
                                        backgroundColor: isSubItemActive
                                          ? themeMode === 'dark'
                                            ? '#1e40af'
                                            : '#3b82f6'
                                          : 'transparent',
                                        '&:hover': {
                                          backgroundColor: isSubItemActive
                                            ? themeMode === 'dark'
                                              ? '#1d4ed8'
                                              : '#2563eb'
                                            : themeMode === 'dark'
                                              ? '#1e293b'
                                              : '#f1f5f9',
                                        },
                                      },
                                    }}
                                  >
                                    <ListItemIcon
                                      sx={{
                                        minWidth: 0,
                                        justifyContent: 'center',
                                        color: isSubItemActive
                                          ? 'white'
                                          : 'inherit',
                                      }}
                                    >
                                      {subItem.icon || (
                                        <Box
                                          sx={{
                                            width: 8,
                                            height: 8,
                                            borderRadius: '50%',
                                            backgroundColor: isSubItemActive
                                              ? 'white'
                                              : themeMode === 'dark'
                                                ? '#64748b'
                                                : '#94a3b8',
                                          }}
                                        />
                                      )}
                                    </ListItemIcon>
                                  </ListItemButton>
                                </Tooltip>
                              </Box>
                            )}
                          </ListItem>
                        );
                      })}
                    </List>
                  </Collapse>
                )}
              </React.Fragment>
            );
          })}
        </List>
      </Box>
    </Drawer>
  );
};

export default LeftNavigationDrawer;
