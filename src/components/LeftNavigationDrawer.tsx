import React, { useState } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  IconButton,
  Box,
  Tooltip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  Assessment as ReportsIcon,
  ExpandLess,
  ExpandMore,
  ChevronLeft,
  ChevronRight,
  Person as PersonIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { toggleDrawerExpanded } from '../store/slices/uiSlice';

interface SubMenuItem {
  id: string;
  label: string;
  path: string;
  icon?: React.ReactNode;
}

interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ReactNode;
  subItems?: SubMenuItem[];
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'dashboard',
    path: '/',
    icon: <DashboardIcon />,
  },
  {
    id: 'users',
    label: 'users',
    path: '/users',
    icon: <PeopleIcon />,
    subItems: [
      {
        id: 'user-list',
        label: 'User List',
        path: '/users/list',
        icon: <PersonIcon />,
      },
      {
        id: 'user-roles',
        label: 'User Roles',
        path: '/users/roles',
        icon: <SecurityIcon />,
      },
    ],
  },
  {
    id: 'reports',
    label: 'reports',
    path: '/reports',
    icon: <ReportsIcon />,
    subItems: [
      {
        id: 'sales-report',
        label: 'Sales Report',
        path: '/reports/sales',
      },
      {
        id: 'user-report',
        label: 'User Report',
        path: '/reports/users',
      },
    ],
  },
  {
    id: 'settings',
    label: 'settings',
    path: '/settings',
    icon: <SettingsIcon />,
    subItems: [
      {
        id: 'general-settings',
        label: 'General',
        path: '/settings/general',
      },
      {
        id: 'notification-settings',
        label: 'Notifications',
        path: '/settings/notifications',
        icon: <NotificationsIcon />,
      },
    ],
  },
];

const DRAWER_WIDTH = 280;
const DRAWER_WIDTH_COLLAPSED = 64;

const LeftNavigationDrawer: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { drawerOpen, drawerExpanded } = useAppSelector((state) => state.ui);
  const { mode: themeMode } = useAppSelector((state) => state.theme);
  
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const handleItemClick = (item: NavigationItem) => {
    if (item.subItems && item.subItems.length > 0) {
      // Toggle expansion for items with sub-items
      setExpandedItems(prev => 
        prev.includes(item.id) 
          ? prev.filter(id => id !== item.id)
          : [...prev, item.id]
      );
    } else {
      // Navigate for items without sub-items
      navigate(item.path);
    }
  };

  const handleSubItemClick = (subItem: SubMenuItem) => {
    navigate(subItem.path);
  };

  const handleToggleExpanded = () => {
    dispatch(toggleDrawerExpanded());
  };

  const isItemActive = (path: string) => {
    return location.pathname === path;
  };

  const isParentActive = (item: NavigationItem) => {
    if (isItemActive(item.path)) return true;
    if (item.subItems) {
      return item.subItems.some(subItem => isItemActive(subItem.path));
    }
    return false;
  };

  const drawerWidth = drawerExpanded ? DRAWER_WIDTH : DRAWER_WIDTH_COLLAPSED;

  return (
    <Drawer
      variant="persistent"
      anchor="left"
      open={drawerOpen}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          backgroundColor: themeMode === 'dark' ? '#0f172a' : '#f8fafc',
          borderRight: `1px solid ${themeMode === 'dark' ? '#334155' : '#e2e8f0'}`,
          transition: 'width 0.3s ease',
          marginTop: '64px', // Account for header height
        },
      }}
    >
      <Box sx={{ overflow: 'auto', height: '100%' }}>
        {/* Toggle Button */}
        <Box className="flex justify-end p-2">
          <IconButton onClick={handleToggleExpanded} size="small">
            {drawerExpanded ? <ChevronLeft /> : <ChevronRight />}
          </IconButton>
        </Box>

        <List>
          {navigationItems.map((item) => {
            const isExpanded = expandedItems.includes(item.id);
            const isActive = isParentActive(item);
            const hasSubItems = item.subItems && item.subItems.length > 0;

            return (
              <React.Fragment key={item.id}>
                <ListItem disablePadding>
                  <Tooltip 
                    title={drawerExpanded ? '' : t(item.label)} 
                    placement="right"
                    arrow
                  >
                    <ListItemButton
                      onClick={() => handleItemClick(item)}
                      selected={isActive}
                      sx={{
                        minHeight: 48,
                        justifyContent: drawerExpanded ? 'initial' : 'center',
                        px: 2.5,
                        backgroundColor: isActive 
                          ? (themeMode === 'dark' ? '#1e40af' : '#3b82f6')
                          : 'transparent',
                        '&:hover': {
                          backgroundColor: themeMode === 'dark' ? '#1e293b' : '#f1f5f9',
                        },
                        '&.Mui-selected': {
                          backgroundColor: isActive 
                            ? (themeMode === 'dark' ? '#1e40af' : '#3b82f6')
                            : 'transparent',
                          '&:hover': {
                            backgroundColor: isActive 
                              ? (themeMode === 'dark' ? '#1d4ed8' : '#2563eb')
                              : (themeMode === 'dark' ? '#1e293b' : '#f1f5f9'),
                          },
                        },
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          minWidth: 0,
                          mr: drawerExpanded ? 3 : 'auto',
                          justifyContent: 'center',
                          color: isActive ? 'white' : 'inherit',
                        }}
                      >
                        {item.icon}
                      </ListItemIcon>
                      
                      {drawerExpanded && (
                        <>
                          <ListItemText 
                            primary={t(item.label)}
                            sx={{
                              color: isActive ? 'white' : 'inherit',
                            }}
                          />
                          {hasSubItems && (
                            isExpanded ? <ExpandLess /> : <ExpandMore />
                          )}
                        </>
                      )}
                    </ListItemButton>
                  </Tooltip>
                </ListItem>

                {/* Sub-items */}
                {hasSubItems && drawerExpanded && (
                  <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                    <List component="div" disablePadding>
                      {item.subItems!.map((subItem) => {
                        const isSubItemActive = isItemActive(subItem.path);
                        
                        return (
                          <ListItem key={subItem.id} disablePadding>
                            <ListItemButton
                              onClick={() => handleSubItemClick(subItem)}
                              selected={isSubItemActive}
                              sx={{
                                pl: 4,
                                minHeight: 40,
                                backgroundColor: isSubItemActive 
                                  ? (themeMode === 'dark' ? '#1e40af' : '#3b82f6')
                                  : 'transparent',
                                '&:hover': {
                                  backgroundColor: themeMode === 'dark' ? '#1e293b' : '#f1f5f9',
                                },
                                '&.Mui-selected': {
                                  backgroundColor: isSubItemActive 
                                    ? (themeMode === 'dark' ? '#1e40af' : '#3b82f6')
                                    : 'transparent',
                                  '&:hover': {
                                    backgroundColor: isSubItemActive 
                                      ? (themeMode === 'dark' ? '#1d4ed8' : '#2563eb')
                                      : (themeMode === 'dark' ? '#1e293b' : '#f1f5f9'),
                                  },
                                },
                              }}
                            >
                              {subItem.icon && (
                                <ListItemIcon
                                  sx={{
                                    minWidth: 0,
                                    mr: 2,
                                    color: isSubItemActive ? 'white' : 'inherit',
                                  }}
                                >
                                  {subItem.icon}
                                </ListItemIcon>
                              )}
                              <ListItemText 
                                primary={subItem.label}
                                sx={{
                                  color: isSubItemActive ? 'white' : 'inherit',
                                }}
                              />
                            </ListItemButton>
                          </ListItem>
                        );
                      })}
                    </List>
                  </Collapse>
                )}
              </React.Fragment>
            );
          })}
        </List>
      </Box>
    </Drawer>
  );
};

export default LeftNavigationDrawer;
