# ResizableCard Component

A reusable Material-UI Paper component that provides resizable functionality with a drag handle in the bottom-right corner.

## Features

- ✅ **Resizable** - Users can resize both width and height by dragging from the bottom-right corner
- ✅ **Draggable** - Users can drag cards to reorder them (optional)
- ✅ **Flexible Content** - Accepts both string content and React components
- ✅ **Customizable** - Supports custom styling, sizing constraints, and elevation
- ✅ **Visual Feedback** - Shows resize handle on hover and drag handle when draggable
- ✅ **Responsive** - Works well within grid layouts and responsive designs

## Props

| Prop          | Type                           | Default   | Description                                              |
| ------------- | ------------------------------ | --------- | -------------------------------------------------------- |
| `title`       | `string`                       | -         | **Required.** The title displayed at the top of the card |
| `children`    | `React.ReactNode`              | -         | **Required.** Content to display in the card body        |
| `elevation`   | `number`                       | `2`       | Material-UI Paper elevation (shadow depth)               |
| `minWidth`    | `string`                       | `'250px'` | Minimum width constraint for resizing                    |
| `minHeight`   | `string`                       | `'150px'` | Minimum height constraint for resizing                   |
| `maxWidth`    | `string`                       | `'100%'`  | Maximum width constraint for resizing                    |
| `sx`          | `SxProps<Theme>`               | `{}`      | Additional Material-UI sx styling                        |
| `draggable`   | `boolean`                      | `false`   | Enables drag-and-drop functionality                      |
| `onDragStart` | `(e: React.DragEvent) => void` | -         | Callback when drag starts                                |
| `onDragOver`  | `(e: React.DragEvent) => void` | -         | Callback when dragging over the card                     |
| `onDrop`      | `(e: React.DragEvent) => void` | -         | Callback when dropped on the card                        |
| `onDragEnd`   | `() => void`                   | -         | Callback when drag ends                                  |
| `isDragging`  | `boolean`                      | `false`   | Visual state indicating if card is being dragged         |

## Usage Examples

### Basic Usage with String Content

```tsx
import ResizableCard from '../components/ResizableCard';

<ResizableCard title="Welcome">
  This is simple text content that will be automatically wrapped in a Typography
  component.
</ResizableCard>;
```

### Advanced Usage with Component Content

```tsx
import { Box, Button, Chip, Stack } from '@mui/material';
import ResizableCard from '../components/ResizableCard';

<ResizableCard title="Interactive Card">
  <Box>
    <Typography variant="body2" gutterBottom>
      This card contains interactive elements:
    </Typography>
    <Stack spacing={2}>
      <Chip label="Status: Active" color="success" />
      <Button variant="contained">Action Button</Button>
    </Stack>
  </Box>
</ResizableCard>;
```

### Custom Styling and Constraints

```tsx
<ResizableCard
  title="Custom Card"
  elevation={4}
  minWidth="300px"
  minHeight="200px"
  sx={{
    backgroundColor: 'primary.light',
    color: 'primary.contrastText',
  }}
>
  Custom styled content with larger minimum size.
</ResizableCard>
```

### Draggable Cards with Reordering

```tsx
const [cards, setCards] = useState([
  { id: '1', title: 'Card 1', content: 'Content 1' },
  { id: '2', title: 'Card 2', content: 'Content 2' },
  { id: '3', title: 'Card 3', content: 'Content 3' },
]);

const [draggedCard, setDraggedCard] = useState(null);

const handleDragStart = (e, cardId) => {
  setDraggedCard(cardId);
};

const handleDrop = (e, targetCardId) => {
  // Reorder logic here
  const draggedIndex = cards.findIndex(card => card.id === draggedCard);
  const targetIndex = cards.findIndex(card => card.id === targetCardId);

  const newCards = [...cards];
  const [draggedItem] = newCards.splice(draggedIndex, 1);
  newCards.splice(targetIndex, 0, draggedItem);

  setCards(newCards);
  setDraggedCard(null);
};

return (
  <Grid container spacing={3}>
    {cards.map(card => (
      <Grid item xs={12} md={6} lg={4} key={card.id}>
        <ResizableCard
          title={card.title}
          draggable={true}
          onDragStart={e => handleDragStart(e, card.id)}
          onDrop={e => handleDrop(e, card.id)}
          isDragging={draggedCard === card.id}
        >
          {card.content}
        </ResizableCard>
      </Grid>
    ))}
  </Grid>
);
```

## Implementation Details

### Resize Functionality

- Uses CSS `resize: 'both'` property for native browser resize behavior
- Requires `overflow: 'auto'` to enable the resize functionality
- Provides visual resize handle using CSS `::after` pseudo-element

### Content Handling

- **String content**: Automatically wrapped in `<Typography variant="body1">`
- **Component content**: Rendered as-is, allowing full flexibility

### Visual Design

- Resize handle appears as diagonal lines in the bottom-right corner
- Handle opacity increases on hover (0.3 → 0.6)
- Uses `currentColor` to match the current theme
- Handle is non-interactive (`pointerEvents: 'none'`) to avoid conflicts

## Browser Support

- ✅ **Modern browsers** - Full resize support (Chrome, Firefox, Safari, Edge)
- ⚠️ **Older browsers** - Cards remain functional but may not show resize handles

## Integration with Grid Systems

Works seamlessly with Material-UI Grid:

```tsx
<Grid container spacing={3}>
  <Grid item xs={12} md={6} lg={4}>
    <ResizableCard title="Card 1">Content 1</ResizableCard>
  </Grid>
  <Grid item xs={12} md={6} lg={4}>
    <ResizableCard title="Card 2">Content 2</ResizableCard>
  </Grid>
</Grid>
```

## Accessibility

- Maintains all Material-UI Paper accessibility features
- Title uses semantic heading structure
- Resize functionality follows native browser accessibility patterns
