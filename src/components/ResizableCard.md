# ResizableCard Component

A reusable Material-UI Paper component that provides resizable functionality with a drag handle in the bottom-right corner.

## Features

- ✅ **Resizable** - Users can resize both width and height by dragging from the bottom-right corner
- ✅ **Flexible Content** - Accepts both string content and React components
- ✅ **Customizable** - Supports custom styling, sizing constraints, and elevation
- ✅ **Visual Feedback** - Shows resize handle on hover
- ✅ **Responsive** - Works well within grid layouts and responsive designs

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | `string` | - | **Required.** The title displayed at the top of the card |
| `children` | `React.ReactNode` | - | **Required.** Content to display in the card body |
| `elevation` | `number` | `2` | Material-UI Paper elevation (shadow depth) |
| `minWidth` | `string` | `'250px'` | Minimum width constraint for resizing |
| `minHeight` | `string` | `'150px'` | Minimum height constraint for resizing |
| `maxWidth` | `string` | `'100%'` | Maximum width constraint for resizing |
| `sx` | `SxProps<Theme>` | `{}` | Additional Material-UI sx styling |

## Usage Examples

### Basic Usage with String Content

```tsx
import ResizableCard from '../components/ResizableCard';

<ResizableCard title="Welcome">
  This is simple text content that will be automatically wrapped in a Typography component.
</ResizableCard>
```

### Advanced Usage with Component Content

```tsx
import { Box, Button, Chip, Stack } from '@mui/material';
import ResizableCard from '../components/ResizableCard';

<ResizableCard title="Interactive Card">
  <Box>
    <Typography variant="body2" gutterBottom>
      This card contains interactive elements:
    </Typography>
    <Stack spacing={2}>
      <Chip label="Status: Active" color="success" />
      <Button variant="contained">Action Button</Button>
    </Stack>
  </Box>
</ResizableCard>
```

### Custom Styling and Constraints

```tsx
<ResizableCard 
  title="Custom Card"
  elevation={4}
  minWidth="300px"
  minHeight="200px"
  sx={{ 
    backgroundColor: 'primary.light',
    color: 'primary.contrastText'
  }}
>
  Custom styled content with larger minimum size.
</ResizableCard>
```

## Implementation Details

### Resize Functionality
- Uses CSS `resize: 'both'` property for native browser resize behavior
- Requires `overflow: 'auto'` to enable the resize functionality
- Provides visual resize handle using CSS `::after` pseudo-element

### Content Handling
- **String content**: Automatically wrapped in `<Typography variant="body1">`
- **Component content**: Rendered as-is, allowing full flexibility

### Visual Design
- Resize handle appears as diagonal lines in the bottom-right corner
- Handle opacity increases on hover (0.3 → 0.6)
- Uses `currentColor` to match the current theme
- Handle is non-interactive (`pointerEvents: 'none'`) to avoid conflicts

## Browser Support

- ✅ **Modern browsers** - Full resize support (Chrome, Firefox, Safari, Edge)
- ⚠️ **Older browsers** - Cards remain functional but may not show resize handles

## Integration with Grid Systems

Works seamlessly with Material-UI Grid:

```tsx
<Grid container spacing={3}>
  <Grid item xs={12} md={6} lg={4}>
    <ResizableCard title="Card 1">Content 1</ResizableCard>
  </Grid>
  <Grid item xs={12} md={6} lg={4}>
    <ResizableCard title="Card 2">Content 2</ResizableCard>
  </Grid>
</Grid>
```

## Accessibility

- Maintains all Material-UI Paper accessibility features
- Title uses semantic heading structure
- Resize functionality follows native browser accessibility patterns
