import React from 'react';
import { AgGridReact } from 'ag-grid-react';
import { ColDef } from 'ag-grid-community';
import { Box, useTheme } from '@mui/material';

/**
 * Reusable DataGrid component with automatic light/dark theme support
 *
 * @example
 * ```tsx
 * <DataGrid
 *   rowData={[
 *     { id: 1, name: '<PERSON>', email: '<EMAIL>' },
 *     { id: 2, name: '<PERSON>', email: '<EMAIL>' }
 *   ]}
 *   columnDefs={[
 *     { field: 'id', headerName: 'ID', width: 60 },
 *     { field: 'name', headerName: 'Name', width: 120 },
 *     { field: 'email', headerName: 'Email', width: 200 }
 *   ]}
 *   height="300px"
 *   enableSorting={true}
 *   enableFiltering={true}
 * />
 * ```
 */
interface DataGridProps {
  /** Array of data objects to display in the grid */
  rowData: any[];
  /** Column definitions with field mappings and display options */
  columnDefs: ColDef[];
  /** Height of the grid container (default: '200px') */
  height?: string | number;
  /** Callback when a row is selected */
  onRowSelected?: (event: any) => void;
  /** Callback when a cell is clicked */
  onCellClicked?: (event: any) => void;
  /** Row selection mode (default: 'single') */
  rowSelection?: 'single' | 'multiple';
  /** Enable column sorting (default: true) */
  enableSorting?: boolean;
  /** Enable column filtering (default: true) */
  enableFiltering?: boolean;
  /** Enable column resizing (default: true) */
  enableResizing?: boolean;
  /** Enable row animations (default: true) */
  animateRows?: boolean;
}

const DataGrid: React.FC<DataGridProps> = ({
  rowData,
  columnDefs,
  height = '200px',
  onRowSelected,
  onCellClicked,
  rowSelection = 'single',
  enableSorting = true,
  enableFiltering = true,
  enableResizing = true,
  animateRows = true,
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Apply theme-aware class
  const themeClass = isDarkMode ? 'ag-theme-alpine-dark' : 'ag-theme-alpine';

  return (
    <Box sx={{ width: '100%' }}>
      <div
        className={themeClass}
        style={{
          height: typeof height === 'number' ? `${height}px` : height,
          width: '100%',
        }}
      >
        <AgGridReact
          rowData={rowData}
          columnDefs={columnDefs}
          defaultColDef={{
            resizable: enableResizing,
            sortable: enableSorting,
            filter: enableFiltering,
          }}
          animateRows={animateRows}
          rowSelection={rowSelection}
          onRowSelected={onRowSelected}
          onCellClicked={onCellClicked}
          suppressRowClickSelection={false}
          headerHeight={35}
          rowHeight={32}
        />
      </div>
    </Box>
  );
};

export default DataGrid;
