import React from 'react';
import { AgGridReact } from 'ag-grid-react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import { Box } from '@mui/material';

const TableTest: React.FC = () => {
  const rowData = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'Active' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'User', status: 'Active' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Manager', status: 'Inactive' },
    { id: 4, name: '<PERSON>', email: '<EMAIL>', role: 'User', status: 'Active' },
    { id: 5, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'Pending' }
  ];

  const columnDefs = [
    { field: 'id', headerName: 'ID', width: 70, sortable: true },
    { field: 'name', headerName: 'Name', width: 120, sortable: true, filter: true },
    { field: 'email', headerName: 'Email', width: 180, sortable: true, filter: true },
    { field: 'role', headerName: 'Role', width: 100, sortable: true, filter: true },
    { field: 'status', headerName: 'Status', width: 100, sortable: true, filter: true }
  ];

  return (
    <Box sx={{ height: '400px', width: '100%', p: 2 }}>
      <div className="ag-theme-alpine" style={{ height: '100%', width: '100%' }}>
        <AgGridReact
          rowData={rowData}
          columnDefs={columnDefs}
          defaultColDef={{
            resizable: true,
            sortable: true,
            filter: true,
          }}
          animateRows={true}
          rowSelection="single"
        />
      </div>
    </Box>
  );
};

export default TableTest;
