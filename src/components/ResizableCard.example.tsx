import React from 'react';
import { <PERSON>, Button, Chip, Stack, Typography, List, ListItem, ListItemText } from '@mui/material';
import ResizableCard from './ResizableCard';

// Example usage of ResizableCard component
const ResizableCardExamples: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        ResizableCard Component Examples
      </Typography>

      <Stack spacing={3}>
        {/* Example 1: Simple string content */}
        <ResizableCard title="Simple Text Card">
          This is a simple text content that will be automatically wrapped in a Typography component.
        </ResizableCard>

        {/* Example 2: Complex component content */}
        <ResizableCard title="Interactive Card">
          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              This card contains interactive elements:
            </Typography>
            <Stack spacing={2} sx={{ mt: 2 }}>
              <Stack direction="row" spacing={1}>
                <Chip label="Status: Active" color="success" size="small" />
                <Chip label="Priority: High" color="error" size="small" />
              </Stack>
              <Stack direction="row" spacing={1}>
                <Button variant="contained" size="small">
                  Primary Action
                </Button>
                <Button variant="outlined" size="small">
                  Secondary
                </Button>
              </Stack>
            </Stack>
          </Box>
        </ResizableCard>

        {/* Example 3: List content */}
        <ResizableCard title="List Card" minHeight="200px">
          <List dense>
            <ListItem>
              <ListItemText primary="Item 1" secondary="Description for item 1" />
            </ListItem>
            <ListItem>
              <ListItemText primary="Item 2" secondary="Description for item 2" />
            </ListItem>
            <ListItem>
              <ListItemText primary="Item 3" secondary="Description for item 3" />
            </ListItem>
          </List>
        </ResizableCard>

        {/* Example 4: Custom styling */}
        <ResizableCard 
          title="Custom Styled Card"
          elevation={4}
          minWidth="300px"
          minHeight="180px"
          sx={{ 
            backgroundColor: 'primary.light',
            color: 'primary.contrastText',
            '& .MuiTypography-h6': {
              color: 'primary.contrastText'
            }
          }}
        >
          <Typography variant="body2">
            This card has custom styling applied through the sx prop.
            It demonstrates how you can customize the appearance while
            maintaining the resizable functionality.
          </Typography>
        </ResizableCard>
      </Stack>
    </Box>
  );
};

export default ResizableCardExamples;
