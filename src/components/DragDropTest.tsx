import React, { useState } from 'react';
import { Box, Grid, Typography } from '@mui/material';
import ResizableCard from './ResizableCard';

const DragDropTest: React.FC = () => {
  const [cards, setCards] = useState([
    { id: '1', title: 'Card 1', content: 'This is card 1' },
    { id: '2', title: 'Card 2', content: 'This is card 2' },
    { id: '3', title: 'Card 3', content: 'This is card 3' }
  ]);

  const [draggedCard, setDraggedCard] = useState<string | null>(null);

  const handleDragStart = (e: React.DragEvent, cardId: string) => {
    setDraggedCard(cardId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetCardId: string) => {
    e.preventDefault();
    
    if (!draggedCard || draggedCard === targetCardId) return;

    const draggedIndex = cards.findIndex(card => card.id === draggedCard);
    const targetIndex = cards.findIndex(card => card.id === targetCardId);

    if (draggedIndex === -1 || targetIndex === -1) return;

    const newCards = [...cards];
    const [draggedItem] = newCards.splice(draggedIndex, 1);
    newCards.splice(targetIndex, 0, draggedItem);

    setCards(newCards);
    setDraggedCard(null);
  };

  const handleDragEnd = () => {
    setDraggedCard(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Drag & Drop Test
      </Typography>
      
      <Grid container spacing={3}>
        {cards.map((card) => (
          <Grid item xs={12} md={4} key={card.id}>
            <ResizableCard
              title={card.title}
              draggable={true}
              onDragStart={(e: React.DragEvent) => handleDragStart(e, card.id)}
              onDragOver={handleDragOver}
              onDrop={(e: React.DragEvent) => handleDrop(e, card.id)}
              onDragEnd={handleDragEnd}
              isDragging={draggedCard === card.id}
            >
              {card.content}
            </ResizableCard>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default DragDropTest;
