import React from 'react';
import { Box, CssBaseline, ThemeProvider, createTheme } from '@mui/material';
import { useAppSelector } from '../hooks/redux';
import Header from './Header';
import LeftNavigationDrawer from './LeftNavigationDrawer';

interface LayoutProps {
  children: React.ReactNode;
  currentPageTitle?: string;
  breadcrumbs?: string[];
}

const Layout: React.FC<LayoutProps> = ({
  children,
  currentPageTitle = 'Dashboard',
  breadcrumbs = [],
}) => {
  const { mode: themeMode, customColors } = useAppSelector(
    state => state.theme
  );
  const { drawerExpanded } = useAppSelector(state => state.ui);

  // Create Material-UI theme based on current theme mode
  const theme = createTheme({
    palette: {
      mode: themeMode === 'light' ? 'light' : 'dark',
      ...(themeMode === 'custom' &&
        customColors && {
          primary: {
            main: customColors.primary,
          },
          secondary: {
            main: customColors.secondary,
          },
          background: {
            default: customColors.background,
            paper: customColors.surface,
          },
          text: {
            primary: customColors.text,
          },
        }),
    },
    typography: {
      fontFamily: '"Roboto", "Poppins", "Helvetica", "Arial", sans-serif',
      h1: {
        fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
      },
      h2: {
        fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
      },
      h3: {
        fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
      },
      h4: {
        fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
      },
      h5: {
        fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
      },
      h6: {
        fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
      },
    },
  });

  const drawerWidth = drawerExpanded ? 280 : 64;

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ display: 'flex' }}>
        <CssBaseline />

        {/* Header */}
        <Header currentPageTitle={currentPageTitle} breadcrumbs={breadcrumbs} />

        {/* Left Navigation Drawer */}
        <LeftNavigationDrawer />

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 3,
            marginTop: '64px', // Account for header height
            marginLeft: `${drawerWidth}px`, // Always account for drawer width
            transition: 'margin-left 0.3s ease',
            backgroundColor: theme.palette.background.default,
            minHeight: 'calc(100vh - 64px)',
          }}
        >
          {children}
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default Layout;
