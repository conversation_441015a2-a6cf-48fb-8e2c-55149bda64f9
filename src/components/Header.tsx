import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import { Home, Info, ContactMail } from '@mui/icons-material';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const Header: React.FC = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();

  const handleLanguageChange = (event: any) => {
    i18n.changeLanguage(event.target.value);
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <AppBar position="static" className="bg-blue-600">
      <Toolbar className="flex justify-between">
        <Typography variant="h6" component="div" className="font-poppins">
          React App
        </Typography>
        
        <Box className="flex items-center space-x-4">
          <Button
            color="inherit"
            component={Link}
            to="/"
            startIcon={<Home />}
            className={isActive('/') ? 'bg-blue-700' : ''}
          >
            {t('home')}
          </Button>
          
          <Button
            color="inherit"
            component={Link}
            to="/about"
            startIcon={<Info />}
            className={isActive('/about') ? 'bg-blue-700' : ''}
          >
            {t('about')}
          </Button>
          
          <Button
            color="inherit"
            component={Link}
            to="/contact"
            startIcon={<ContactMail />}
            className={isActive('/contact') ? 'bg-blue-700' : ''}
          >
            {t('contact')}
          </Button>

          <FormControl size="small" className="min-w-[100px]">
            <InputLabel className="text-white">{t('language')}</InputLabel>
            <Select
              value={i18n.language}
              onChange={handleLanguageChange}
              className="text-white"
              sx={{
                color: 'white',
                '.MuiOutlinedInput-notchedOutline': {
                  borderColor: 'white',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'white',
                },
                '.MuiSvgIcon-root': {
                  color: 'white',
                },
              }}
            >
              <MenuItem value="en">English</MenuItem>
              <MenuItem value="es">Español</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
