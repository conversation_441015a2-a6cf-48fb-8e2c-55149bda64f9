import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON>,
  IconButton,
  Typography,
  Box,
  Badge,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Avatar,
} from '@mui/material';
import {
  Menu as MenuIcon,
  MenuOpen as MenuOpenIcon,
  Notifications as NotificationsIcon,
  MoreVert as MoreVertIcon,
  Language as LanguageIcon,
  Palette as PaletteIcon,
  Logout as LogoutIcon,
  LightMode,
  DarkMode,
  Settings,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Assessment as ReportsIcon,
  Settings as SettingsIcon,
  Person as PersonIcon,
  Security as SecurityIcon,
  TrendingUp as SalesIcon,
  Analytics as UserReportIcon,
  Tune as GeneralIcon,
  Notifications as NotificationSettingsIcon,
  ChevronRight,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { useConfig } from '../contexts/ConfigContext';
import { toggleDrawerExpanded } from '../store/slices/uiSlice';
import { setThemeMode } from '../store/slices/themeSlice';
import type { ThemeMode } from '../store/slices/themeSlice';

// Breadcrumb item interface
interface BreadcrumbItem {
  label: string;
  path: string;
  icon: React.ReactNode;
  isTranslationKey?: boolean;
}

// Breadcrumb configuration for all routes
const getBreadcrumbs = (
  pathname: string,
  t: (key: string) => string
): BreadcrumbItem[] => {
  const breadcrumbMap: { [key: string]: BreadcrumbItem[] } = {
    '/': [
      {
        label: t('dashboard'),
        path: '/',
        icon: <DashboardIcon />,
        isTranslationKey: true,
      },
    ],
    '/users': [
      {
        label: t('users'),
        path: '/users',
        icon: <PeopleIcon />,
        isTranslationKey: true,
      },
    ],
    '/users/list': [
      {
        label: t('users'),
        path: '/users',
        icon: <PeopleIcon />,
        isTranslationKey: true,
      },
      {
        label: t('userList'),
        path: '/users/list',
        icon: <PersonIcon />,
        isTranslationKey: true,
      },
    ],
    '/users/roles': [
      {
        label: t('users'),
        path: '/users',
        icon: <PeopleIcon />,
        isTranslationKey: true,
      },
      {
        label: t('userRoles'),
        path: '/users/roles',
        icon: <SecurityIcon />,
        isTranslationKey: true,
      },
    ],
    '/reports': [
      {
        label: t('reports'),
        path: '/reports',
        icon: <ReportsIcon />,
        isTranslationKey: true,
      },
    ],
    '/reports/sales': [
      {
        label: t('reports'),
        path: '/reports',
        icon: <ReportsIcon />,
        isTranslationKey: true,
      },
      {
        label: t('salesReport'),
        path: '/reports/sales',
        icon: <SalesIcon />,
        isTranslationKey: true,
      },
    ],
    '/reports/users': [
      {
        label: t('reports'),
        path: '/reports',
        icon: <ReportsIcon />,
        isTranslationKey: true,
      },
      {
        label: t('userReport'),
        path: '/reports/users',
        icon: <UserReportIcon />,
        isTranslationKey: true,
      },
    ],
    '/settings': [
      {
        label: t('settings'),
        path: '/settings',
        icon: <SettingsIcon />,
        isTranslationKey: true,
      },
    ],
    '/settings/general': [
      {
        label: t('settings'),
        path: '/settings',
        icon: <SettingsIcon />,
        isTranslationKey: true,
      },
      {
        label: t('general'),
        path: '/settings/general',
        icon: <GeneralIcon />,
        isTranslationKey: true,
      },
    ],
    '/settings/notifications': [
      {
        label: t('settings'),
        path: '/settings',
        icon: <SettingsIcon />,
        isTranslationKey: true,
      },
      {
        label: t('notifications'),
        path: '/settings/notifications',
        icon: <NotificationSettingsIcon />,
        isTranslationKey: true,
      },
    ],
  };

  return (
    breadcrumbMap[pathname] || [
      {
        label: t('dashboard'),
        path: '/',
        icon: <DashboardIcon />,
        isTranslationKey: true,
      },
    ]
  );
};

// Flag emojis for language selection
const languageOptions = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'zh', name: '中文', flag: '🇨🇳' },
];

const themeOptions = [
  { mode: 'light' as ThemeMode, name: 'Light', icon: <LightMode /> },
  { mode: 'dark' as ThemeMode, name: 'Dark', icon: <DarkMode /> },
  { mode: 'custom' as ThemeMode, name: 'Custom', icon: <Settings /> },
];

interface HeaderProps {}

const Header: React.FC<HeaderProps> = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { config } = useConfig();
  const { hasNotifications, notificationCount, drawerExpanded } =
    useAppSelector(state => state.ui);
  const { mode: currentTheme } = useAppSelector(state => state.theme);

  // Get breadcrumbs for current route
  const currentBreadcrumbs = getBreadcrumbs(location.pathname, t);

  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [languageAnchorEl, setLanguageAnchorEl] = useState<null | HTMLElement>(
    null
  );
  const [themeAnchorEl, setThemeAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleLanguageClick = (event: React.MouseEvent<HTMLElement>) => {
    setLanguageAnchorEl(event.currentTarget);
  };

  const handleThemeClick = (event: React.MouseEvent<HTMLElement>) => {
    setThemeAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleLanguageClose = () => {
    setLanguageAnchorEl(null);
  };

  const handleThemeClose = () => {
    setThemeAnchorEl(null);
  };

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode);
    handleLanguageClose();
  };

  const handleThemeChange = (theme: ThemeMode) => {
    dispatch(setThemeMode(theme));
    handleThemeClose();
  };

  const handleDrawerToggle = () => {
    dispatch(toggleDrawerExpanded());
  };

  const handleLogout = () => {
    // Implement logout logic here
    console.log('Logout clicked');
    handleMenuClose();
  };

  const handleBreadcrumbClick = (path: string) => {
    navigate(path);
  };

  const currentLanguage =
    languageOptions.find(lang => lang.code === i18n.language) ||
    languageOptions[0];

  return (
    <AppBar
      position="fixed"
      sx={{
        zIndex: theme => theme.zIndex.drawer + 1,
        backgroundColor: currentTheme === 'dark' ? '#1e293b' : '#1976d2',
      }}
    >
      <Toolbar className="flex justify-between min-h-16">
        {/* Left Section */}
        <Box className="flex items-center" sx={{ width: 280, gap: 2 }}>
          <IconButton
            color="inherit"
            aria-label="toggle drawer"
            onClick={handleDrawerToggle}
            edge="start"
            className="mr-2"
          >
            {drawerExpanded ? <MenuOpenIcon /> : <MenuIcon />}
          </IconButton>

          <Box className="flex items-center">
            {config.company.companyLogo ? (
              // Full company logo - takes precedence over everything
              <Box
                component="img"
                src={config.company.companyLogo}
                alt="Company Logo"
                sx={{
                  height: 32,
                  width: 'auto',
                  mr: 2,
                  maxWidth: '200px',
                }}
              />
            ) : (
              // Fallback to mini logo + company name or defaults
              <>
                {config.company.companyMiniLogo ? (
                  // Use company mini logo if available
                  <Box
                    component="img"
                    src={config.company.companyMiniLogo}
                    alt="Company Mini Logo"
                    sx={{
                      width: 32,
                      height: 32,
                      mr: 2,
                      borderRadius: '50%',
                      objectFit: 'cover',
                    }}
                  />
                ) : (
                  // Use avatar with first letter of company name or default "C"
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      mr: 2,
                      backgroundColor: 'primary.light',
                    }}
                  >
                    {config.company.companyName
                      ? config.company.companyName.charAt(0).toUpperCase()
                      : 'C'}
                  </Avatar>
                )}

                <Typography
                  variant="h6"
                  noWrap
                  component="div"
                  className="font-poppins font-semibold"
                >
                  {config.company.companyName || 'Company'}
                </Typography>
              </>
            )}
          </Box>
        </Box>

        {/* Center Section - Breadcrumb */}
        <Box
          className="flex-1 flex items-center"
          sx={{ display: { xs: 'none', sm: 'flex' } }}
        >
          {currentBreadcrumbs.map((breadcrumb, index) => (
            <React.Fragment key={breadcrumb.path}>
              {/* Breadcrumb Item */}
              <Box
                className="flex items-center"
                sx={{
                  cursor:
                    index < currentBreadcrumbs.length - 1
                      ? 'pointer'
                      : 'default',
                  color:
                    index < currentBreadcrumbs.length - 1
                      ? 'inherit'
                      : 'rgba(255, 255, 255, 0.7)',
                  '&:hover': {
                    color:
                      index < currentBreadcrumbs.length - 1
                        ? 'rgba(255, 255, 255, 0.8)'
                        : 'inherit',
                  },
                }}
                onClick={() => {
                  if (index < currentBreadcrumbs.length - 1) {
                    handleBreadcrumbClick(breadcrumb.path);
                  }
                }}
              >
                {/* Icon */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mr: 1,
                    fontSize: '1.2rem',
                  }}
                >
                  {breadcrumb.icon}
                </Box>

                {/* Label */}
                <Typography
                  variant="h6"
                  component="span"
                  className="font-roboto"
                  sx={{
                    fontSize: '1rem',
                    fontWeight:
                      index === currentBreadcrumbs.length - 1 ? 600 : 400,
                  }}
                >
                  {breadcrumb.label}
                </Typography>
              </Box>

              {/* Separator */}
              {index < currentBreadcrumbs.length - 1 && (
                <ChevronRight
                  sx={{
                    mx: 1,
                    fontSize: '1.2rem',
                    color: 'rgba(255, 255, 255, 0.5)',
                  }}
                />
              )}
            </React.Fragment>
          ))}
        </Box>

        {/* Right Section */}
        <Box className="flex items-center space-x-2">
          {/* Notifications */}
          <IconButton color="inherit" aria-label="notifications">
            <Badge
              badgeContent={hasNotifications ? notificationCount : 0}
              color="error"
              variant={hasNotifications ? 'standard' : 'dot'}
            >
              <NotificationsIcon />
            </Badge>
          </IconButton>

          {/* Main Menu */}
          <IconButton
            color="inherit"
            aria-label="menu"
            onClick={handleMenuClick}
          >
            <MoreVertIcon />
          </IconButton>

          {/* Main Menu Dropdown */}
          <Menu
            anchorEl={menuAnchorEl}
            open={Boolean(menuAnchorEl)}
            onClose={handleMenuClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            {/* Language Selector */}
            <MenuItem onClick={handleLanguageClick}>
              <ListItemIcon>
                <LanguageIcon />
              </ListItemIcon>
              <ListItemText>
                {currentLanguage.flag} {currentLanguage.name}
              </ListItemText>
            </MenuItem>

            {/* Theme Selector */}
            <MenuItem onClick={handleThemeClick}>
              <ListItemIcon>
                <PaletteIcon />
              </ListItemIcon>
              <ListItemText>{t('theme')}</ListItemText>
            </MenuItem>

            <Divider />

            {/* Logout */}
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <LogoutIcon />
              </ListItemIcon>
              <ListItemText>{t('logout')}</ListItemText>
            </MenuItem>
          </Menu>

          {/* Language Submenu */}
          <Menu
            anchorEl={languageAnchorEl}
            open={Boolean(languageAnchorEl)}
            onClose={handleLanguageClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'left', vertical: 'top' }}
          >
            {languageOptions.map(language => (
              <MenuItem
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                selected={i18n.language === language.code}
              >
                <ListItemText>
                  {language.flag} {language.name}
                </ListItemText>
              </MenuItem>
            ))}
          </Menu>

          {/* Theme Submenu */}
          <Menu
            anchorEl={themeAnchorEl}
            open={Boolean(themeAnchorEl)}
            onClose={handleThemeClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'left', vertical: 'top' }}
          >
            {themeOptions.map(theme => (
              <MenuItem
                key={theme.mode}
                onClick={() => handleThemeChange(theme.mode)}
                selected={currentTheme === theme.mode}
              >
                <ListItemIcon>{theme.icon}</ListItemIcon>
                <ListItemText>{t(theme.mode)}</ListItemText>
              </MenuItem>
            ))}
          </Menu>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
