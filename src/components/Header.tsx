import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Typography,
  Box,
  Badge,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Avatar,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  MoreVert as MoreVertIcon,
  Language as LanguageIcon,
  Palette as PaletteIcon,
  Logout as LogoutIcon,
  LightMode,
  DarkMode,
  Settings,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { toggleDrawer } from '../store/slices/uiSlice';
import { setThemeMode } from '../store/slices/themeSlice';
import type { ThemeMode } from '../store/slices/themeSlice';

// Flag emojis for language selection
const languageOptions = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'zh', name: '中文', flag: '🇨🇳' },
];

const themeOptions = [
  { mode: 'light' as ThemeMode, name: 'Light', icon: <LightMode /> },
  { mode: 'dark' as ThemeMode, name: 'Dark', icon: <DarkMode /> },
  { mode: 'custom' as ThemeMode, name: 'Custom', icon: <Settings /> },
];

interface HeaderProps {
  currentPageTitle?: string;
  breadcrumbs?: string[];
}

const Header: React.FC<HeaderProps> = ({
  currentPageTitle = 'Dashboard',
  breadcrumbs = [],
}) => {
  const { t, i18n } = useTranslation();
  const dispatch = useAppDispatch();
  const { hasNotifications, notificationCount } = useAppSelector(
    state => state.ui
  );
  const { mode: currentTheme } = useAppSelector(state => state.theme);

  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [languageAnchorEl, setLanguageAnchorEl] = useState<null | HTMLElement>(
    null
  );
  const [themeAnchorEl, setThemeAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleLanguageClick = (event: React.MouseEvent<HTMLElement>) => {
    setLanguageAnchorEl(event.currentTarget);
  };

  const handleThemeClick = (event: React.MouseEvent<HTMLElement>) => {
    setThemeAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleLanguageClose = () => {
    setLanguageAnchorEl(null);
  };

  const handleThemeClose = () => {
    setThemeAnchorEl(null);
  };

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode);
    handleLanguageClose();
  };

  const handleThemeChange = (theme: ThemeMode) => {
    dispatch(setThemeMode(theme));
    handleThemeClose();
  };

  const handleDrawerToggle = () => {
    dispatch(toggleDrawer());
  };

  const handleLogout = () => {
    // Implement logout logic here
    console.log('Logout clicked');
    handleMenuClose();
  };

  const currentLanguage =
    languageOptions.find(lang => lang.code === i18n.language) ||
    languageOptions[0];

  return (
    <AppBar
      position="fixed"
      sx={{
        zIndex: theme => theme.zIndex.drawer + 1,
        backgroundColor: currentTheme === 'dark' ? '#1e293b' : '#1976d2',
      }}
    >
      <Toolbar className="flex justify-between min-h-16">
        {/* Left Section */}
        <Box className="flex items-center" sx={{ width: 280 }}>
          <IconButton
            color="inherit"
            aria-label="toggle drawer"
            onClick={handleDrawerToggle}
            edge="start"
            className="mr-2"
          >
            <MenuIcon />
          </IconButton>

          <Box className="flex items-center">
            <Avatar
              sx={{
                width: 32,
                height: 32,
                mr: 2,
                backgroundColor: 'primary.light',
              }}
            >
              C
            </Avatar>
            <Typography
              variant="h6"
              noWrap
              component="div"
              className="font-poppins font-semibold"
            >
              Company
            </Typography>
          </Box>
        </Box>

        {/* Center Section - Breadcrumb/Page Title */}
        <Box className="flex-1 flex items-center justify-center md:justify-start">
          <Typography
            variant="h6"
            component="div"
            className="font-roboto"
            sx={{ display: { xs: 'none', sm: 'block' } }}
          >
            {breadcrumbs.length > 0
              ? breadcrumbs.join(' / ')
              : currentPageTitle}
          </Typography>
        </Box>

        {/* Right Section */}
        <Box className="flex items-center space-x-2">
          {/* Notifications */}
          <IconButton color="inherit" aria-label="notifications">
            <Badge
              badgeContent={hasNotifications ? notificationCount : 0}
              color="error"
              variant={hasNotifications ? 'standard' : 'dot'}
            >
              <NotificationsIcon />
            </Badge>
          </IconButton>

          {/* Main Menu */}
          <IconButton
            color="inherit"
            aria-label="menu"
            onClick={handleMenuClick}
          >
            <MoreVertIcon />
          </IconButton>

          {/* Main Menu Dropdown */}
          <Menu
            anchorEl={menuAnchorEl}
            open={Boolean(menuAnchorEl)}
            onClose={handleMenuClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            {/* Language Selector */}
            <MenuItem onClick={handleLanguageClick}>
              <ListItemIcon>
                <LanguageIcon />
              </ListItemIcon>
              <ListItemText>
                {t('language')} ({currentLanguage.flag} {currentLanguage.name})
              </ListItemText>
            </MenuItem>

            {/* Theme Selector */}
            <MenuItem onClick={handleThemeClick}>
              <ListItemIcon>
                <PaletteIcon />
              </ListItemIcon>
              <ListItemText>{t('theme')}</ListItemText>
            </MenuItem>

            <Divider />

            {/* Logout */}
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <LogoutIcon />
              </ListItemIcon>
              <ListItemText>{t('logout')}</ListItemText>
            </MenuItem>
          </Menu>

          {/* Language Submenu */}
          <Menu
            anchorEl={languageAnchorEl}
            open={Boolean(languageAnchorEl)}
            onClose={handleLanguageClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'left', vertical: 'top' }}
          >
            {languageOptions.map(language => (
              <MenuItem
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                selected={i18n.language === language.code}
              >
                <ListItemText>
                  {language.flag} {language.name}
                </ListItemText>
              </MenuItem>
            ))}
          </Menu>

          {/* Theme Submenu */}
          <Menu
            anchorEl={themeAnchorEl}
            open={Boolean(themeAnchorEl)}
            onClose={handleThemeClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'left', vertical: 'top' }}
          >
            {themeOptions.map(theme => (
              <MenuItem
                key={theme.mode}
                onClick={() => handleThemeChange(theme.mode)}
                selected={currentTheme === theme.mode}
              >
                <ListItemIcon>{theme.icon}</ListItemIcon>
                <ListItemText>{t(theme.mode)}</ListItemText>
              </MenuItem>
            ))}
          </Menu>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
