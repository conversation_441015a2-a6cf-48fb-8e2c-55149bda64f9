import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import Header from '../Header';
import themeReducer from '../../store/slices/themeSlice';
import uiReducer from '../../store/slices/uiSlice';
import '../../i18n';

// Create a test store
const createTestStore = () => {
  return configureStore({
    reducer: {
      theme: themeReducer,
      ui: uiReducer,
    },
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createTestStore();
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('Header Component', () => {
  it('renders company name', () => {
    renderWithProviders(<Header />);
    expect(screen.getByText('Company')).toBeInTheDocument();
  });

  it('renders menu toggle button', () => {
    renderWithProviders(<Header />);
    const menuButton = screen.getByLabelText('toggle drawer');
    expect(menuButton).toBeInTheDocument();
  });

  it('renders notifications icon', () => {
    renderWithProviders(<Header />);
    const notificationButton = screen.getByLabelText('notifications');
    expect(notificationButton).toBeInTheDocument();
  });
});
