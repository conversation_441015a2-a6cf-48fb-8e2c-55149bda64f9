# React SotaUI v4

A modern, responsive React application built with TypeScript, featuring a comprehensive navigation system, internationalization, and a dark/light theme system.

## 🚀 Features

### 🎨 **Modern UI/UX**

- **Dark theme by default** with comfortable color palette
- **Light theme option** with seamless switching
- **Configurable themes** for client customization
- **Responsive design** that adapts to all screen sizes
- **Material Design** components with Tailwind CSS styling

### 🧭 **Advanced Navigation**

- **Collapsible sidebar** with icon-only and expanded modes
- **Multi-level navigation** with expandable sub-menus
- **Active state highlighting** for current page
- **Tooltips** for collapsed navigation items
- **Visual connection lines** showing sub-item relationships
- **Header integration** with dynamic page titles

### 🌍 **Internationalization**

- **Multi-language support**: English, Japanese, Chinese
- **Flag indicators** for language selection
- **Real-time language switching**
- **Localized navigation** and content

### ⚡ **State Management**

- **Redux Toolkit** for predictable state management
- **Theme state** management
- **UI state** management (drawer, notifications)
- **Typed hooks** for type-safe state access

### 🧪 **Testing & Quality**

- **Vitest** for unit testing
- **React Testing Library** for component testing
- **Cypress** for end-to-end testing
- **ESLint + Prettier** for code quality
- **TypeScript** for type safety

## 🛠️ Tech Stack

### **Core Framework**

- **React 19** - Latest React with concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and dev server

### **Styling & UI**

- **Material-UI (MUI)** - React component library
- **Tailwind CSS** - Utility-first CSS framework
- **Material Icons** - Comprehensive icon set
- **Google Fonts** - Roboto and Poppins fonts

### **Routing & Navigation**

- **React Router v6** - Declarative routing

### **State Management**

- **Redux Toolkit** - Modern Redux with less boilerplate
- **React Redux** - React bindings for Redux

### **Internationalization**

- **react-i18next** - Internationalization framework
- **i18next** - Core i18n functionality
- **Language detection** - Automatic language detection

### **Testing**

- **Vitest** - Fast unit test runner
- **React Testing Library** - Component testing utilities
- **Cypress** - End-to-end testing framework
- **@testing-library/jest-dom** - Custom Jest matchers

### **Code Quality**

- **ESLint** - JavaScript/TypeScript linting
- **Prettier** - Code formatting
- **TypeScript ESLint** - TypeScript-specific linting rules

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── Header.tsx       # Application header with navigation
│   ├── LeftNavigationDrawer.tsx  # Sidebar navigation
│   ├── Layout.tsx       # Main layout wrapper
│   └── __tests__/       # Component tests
├── pages/               # Page components
│   ├── Dashboard.tsx    # Dashboard page
│   ├── Users.tsx        # Users management
│   ├── UserList.tsx     # User list sub-page
│   ├── UserRoles.tsx    # User roles sub-page
│   ├── Reports.tsx      # Reports overview
│   ├── SalesReport.tsx  # Sales report page
│   ├── UserReport.tsx   # User report page
│   ├── Settings.tsx     # Settings overview
│   ├── GeneralSettings.tsx      # General settings
│   └── NotificationSettings.tsx # Notification settings
├── store/               # Redux store configuration
│   ├── index.ts         # Store setup
│   └── slices/          # Redux slices
│       ├── counterSlice.ts      # Example counter slice
│       ├── themeSlice.ts        # Theme management
│       └── uiSlice.ts           # UI state management
├── hooks/               # Custom React hooks
│   └── redux.ts         # Typed Redux hooks
├── i18n/                # Internationalization
│   ├── index.ts         # i18n configuration
│   └── locales/         # Translation files
│       ├── en.json      # English translations
│       ├── ja.json      # Japanese translations
│       └── zh.json      # Chinese translations
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
├── assets/              # Static assets
│   ├── images/          # Image files
│   └── icons/           # Icon files
└── test/                # Test configuration
    └── setup.ts         # Test setup file
```

## 🚦 Getting Started

### **Prerequisites**

- Node.js 18+
- npm, yarn, or pnpm

### **Installation**

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd react-sotauiv4
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start development server**

   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

## 📜 Available Scripts

### **Development**

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

### **Code Quality**

- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

### **Testing**

- `npm run test` - Run unit tests
- `npm run test:ui` - Run tests with UI
- `npm run test:coverage` - Run tests with coverage
- `npm run cypress:open` - Open Cypress test runner
- `npm run cypress:run` - Run Cypress tests headlessly

## 🎯 Key Features Explained

### **Navigation System**

The application features a sophisticated navigation system with:

- **Collapsible Sidebar**: Toggle between expanded (280px) and collapsed (64px) modes
- **Multi-level Menus**: Support for nested navigation with visual indicators
- **Smart Tooltips**: Show labels when sidebar is collapsed
- **Active States**: Visual feedback for current page/section
- **Connection Lines**: Visual indicators showing sub-item relationships

### **Theme System**

- **Default Dark Theme**: Comfortable dark color scheme
- **Light Theme**: Clean light alternative
- **Custom Themes**: Configurable color schemes for branding
- **Persistent Settings**: Theme preference saved across sessions

### **Internationalization**

- **3 Languages**: English, Japanese, Chinese
- **Flag Indicators**: Visual language identification
- **Contextual Translation**: Smart translation for navigation vs. content
- **Browser Detection**: Automatic language detection

### **State Management Architecture**

- **Theme Slice**: Manages theme mode and custom colors
- **UI Slice**: Controls drawer state, notifications, and UI interactions
- **Typed Hooks**: Type-safe Redux integration with custom hooks

## 🎨 Design Guidelines

### **Typography**

- **Headings**: Poppins font for visual hierarchy
- **Body Text**: Roboto font for readability
- **Consistent Sizing**: Material-UI typography scale

### **Color Scheme**

- **Dark Theme**: Slate-based color palette
- **Light Theme**: Clean, minimal colors
- **Accent Colors**: Blue-based primary colors
- **Status Colors**: Semantic color usage

### **Spacing & Layout**

- **24px Padding**: Consistent content alignment
- **Material Design**: Following Material Design principles
- **Responsive Breakpoints**: Mobile-first responsive design

## 🧪 Testing Strategy

### **Unit Tests**

- Component rendering tests
- Hook functionality tests
- Utility function tests

### **Integration Tests**

- Navigation flow tests
- State management integration
- Theme switching tests

### **E2E Tests**

- Complete user workflows
- Cross-browser compatibility
- Responsive design validation

## 🚀 Deployment

### **Build for Production**

```bash
npm run build
```

### **Preview Production Build**

```bash
npm run preview
```

The build artifacts will be stored in the `dist/` directory.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Material-UI** for the excellent React components
- **Tailwind CSS** for utility-first styling
- **Redux Toolkit** for simplified state management
- **Vite** for the fast development experience
